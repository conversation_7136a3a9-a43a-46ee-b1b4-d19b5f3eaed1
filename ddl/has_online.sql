ALTER TABLE `piclumen`.`stripe_product`
    ADD COLUMN `first_buy_sub_discount` int default 0 AFTER `initial_lumen`;

ALTER TABLE stripe_product ADD COLUMN price varchar(255) DEFAULT NULL COMMENT '价格' after price_interval;
ALTER TABLE subscription_current ADD COLUMN renew_price varchar(255) DEFAULT NULL COMMENT '价格' after price_interval;

UPDATE `piclumen`.`stripe_product` SET `price` = '0.99' WHERE lumen = 100;
UPDATE `piclumen`.`stripe_product` SET `price` = '8.99' WHERE lumen = 1000;
UPDATE `piclumen`.`stripe_product` SET `price` = '79.99' WHERE lumen = 10000;

UPDATE `piclumen`.`stripe_product` SET `price` = '11.99' WHERE plan_level = 'standard' and price_interval = "month" and `status` = 1;
UPDATE `piclumen`.`stripe_product` SET `price` = '107.88' WHERE plan_level = 'standard' and price_interval = "year" and `status` = 1 ;
UPDATE `piclumen`.`stripe_product` SET `price` = '28.99' WHERE plan_level = 'pro' and price_interval = "month" and `status` = 1;
UPDATE `piclumen`.`stripe_product` SET `price` = '263.88' WHERE plan_level = 'pro' and price_interval = "year" and `status` = 1;
-- -------------------------------2025-06-09----------------------------------
ALTER TABLE pay_apple_product ADD COLUMN status tinyint DEFAULT 1 COMMENT '状态';
ALTER TABLE pay_apple_product ADD COLUMN initial_lumen int DEFAULT 0 COMMENT '状态' after lumen;
INSERT INTO `piclumen`.`pay_apple_product` (`id`, `apple_product_id`, `plan_level`, `lumen`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `mark`, `status`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (9, 'basic.lumens.100', 'basic', 100, 50, 'one', NULL, -1, 'v2', 1, '2025-06-06 17:38:54', '2025-06-06 17:38:54', 'admin', 'admin');
INSERT INTO `piclumen`.`pay_apple_product` (`id`, `apple_product_id`, `plan_level`, `lumen`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `mark`, `status`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (10, 'basic.lumens.1000', 'basic', 1000, 500, 'one', NULL, -1, 'v2', 1, '2025-06-06 17:38:54', '2025-06-06 17:38:54', 'admin', 'admin');
INSERT INTO `piclumen`.`pay_apple_product` (`id`, `apple_product_id`, `plan_level`, `lumen`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `mark`, `status`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (11, 'basic.lumens.10000', 'basic', 10000, 5000, 'one', NULL, -1, 'v2', 1, '2025-06-06 17:38:54', '2025-06-06 17:38:54', 'admin', 'admin');
INSERT INTO `piclumen`.`pay_apple_product` (`id`, `apple_product_id`, `plan_level`, `lumen`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `mark`, `status`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (12, 'standard.lumens.100', 'standard', 100, 50, 'one', NULL, -1, 'v2', 1, '2025-06-06 17:38:54', '2025-06-06 17:38:54', 'admin', 'admin');
INSERT INTO `piclumen`.`pay_apple_product` (`id`, `apple_product_id`, `plan_level`, `lumen`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `mark`, `status`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (13, 'standard.lumens.1000', 'standard', 1000, 500, 'one', NULL, -1, 'v2', 1, '2025-06-06 17:38:54', '2025-06-06 17:38:54', 'admin', 'admin');
INSERT INTO `piclumen`.`pay_apple_product` (`id`, `apple_product_id`, `plan_level`, `lumen`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `mark`, `status`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (14, 'standard.lumens.10000', 'standard', 10000, 5000, 'one', NULL, -1, 'v2', 1, '2025-06-06 17:38:54', '2025-06-06 17:38:54', 'admin', 'admin');
INSERT INTO `piclumen`.`pay_apple_product` (`id`, `apple_product_id`, `plan_level`, `lumen`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `mark`, `status`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (15, 'pro.lumens.100', 'pro', 100, 50, 'one', NULL, -1, 'v2', 1, '2025-06-06 17:38:54', '2025-06-06 17:38:54', 'admin', 'admin');
INSERT INTO `piclumen`.`pay_apple_product` (`id`, `apple_product_id`, `plan_level`, `lumen`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `mark`, `status`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (16, 'pro.lumens.1000', 'pro', 1000, 500, 'one', NULL, -1, 'v2', 1, '2025-06-06 17:38:54', '2025-06-06 17:38:54', 'admin', 'admin');
INSERT INTO `piclumen`.`pay_apple_product` (`id`, `apple_product_id`, `plan_level`, `lumen`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `mark`, `status`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (17, 'pro.lumens.10000', 'pro', 10000, 5000, 'one', NULL, -1, 'v2', 1, '2025-06-06 17:38:54', '2025-06-06 17:38:54', 'admin', 'admin');

