package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.lx.pl.client.BackendApi;
import com.lx.pl.constant.CommandPromptParameters;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.community.entity.TaskLumen;
import com.lx.pl.db.mysql.gen.entity.PromptRecord;
import com.lx.pl.db.mysql.gen.entity.RecordAddress;
import com.lx.pl.db.mysql.gen.entity.SpecifiedCountry;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper;
import com.lx.pl.db.mysql.gen.mapper.SpecifiedCountryMapper;
import com.lx.pl.db.mysql.gen.repository.RecordAddressRepository;
import com.lx.pl.dto.*;
import com.lx.pl.enums.FeaturesType;
import com.lx.pl.exception.OperationNotAllowedException;
import com.lx.pl.lb.LbCacheService;
import com.lx.pl.util.DateUtils;
import com.lx.pl.util.IpUtils;
import com.lx.pl.util.JsonUtils;
import com.lx.pl.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.redis.connection.ReturnType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import retrofit2.Response;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.lx.pl.constant.LockPrefixConstant.FAST_CREATE_LOCK_PREFIX;
import static com.lx.pl.constant.LogicConstants.*;
import static com.lx.pl.service.RedisService.LUA_SCRIPT_SET_HASH_VALUE;

@Service
@Slf4j
public class LoadBalanceService {

    @Value("${realistic.ModelId}")
    String realisticModelId;

    @Value("${anime.ModelId}")
    String animeModelId;

    @Value("${lineart.ModelId}")
    String lineartModelId;

    @Value("${art.ModelId}")
    String artModelId;

    @Value("${fluxschell.modelId}")
    String fluxschellModelId;

    @Value("${fluxdev.modelId}")
    String fluxdevModelId;

    public static final String LOADBALANCE_MODEL = "loadbalance_model";

    public static final String LOADBALANCE_FEATURE = "loadbalance_feature";

    public static final String USER_TASK_COST_TIMES = "user_task_cost_times";

    public static final String USER_TASK_TIMESTAMP = "user_task_timestamp:";

    public static final String MARK_ID_QUEUE_NAME = "mark_id_queue_name";

    public static final String PROMPT_RECORD_DATA = "prompt_record_data";

    public static final String REALISTIC_MODEL_KEY = "realisticModelKey";

    public static final String ANIME_MODEL_KEY = "animeModelKey";

    public static final String LINEART_MODEL_KEY = "lineartModelKey";

    public static final String BLFLUX_QUEUE_KEY_ERROR = "blFluxQueueKeyError";

    public static final String BLCOMMON_QUEUE_KEY_ERROR = "blCommonQueueKeyError";

    public static final String ALL_FEATURE_SERVERIDS = "all_feature_serverIds";

    private Set<String> countrySet = new HashSet<>(Arrays.asList("India", "Indonesia", "Pakistan"));

    @Resource
    private ModelService modelService;

    private final Cache<String, int[][]> cache = CacheBuilder.newBuilder()
            .expireAfterWrite(60, TimeUnit.MINUTES)
            .build();

    private final Cache<String, TreeMap<Integer, Integer>> countryDelayTimeCache = CacheBuilder.newBuilder()
            .expireAfterWrite(60, TimeUnit.MINUTES)
            .build();


    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.LOWER_CAMEL_CASE);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    @Value("${fast.hours}")
    Long fastHours;

    @Autowired
    RedisService redisService;

    @Autowired
    BackendApi backendApi;

    @Resource
    GeoIpCountryService geoIpCountryService;

    @Resource
    PromptRecordMapper promptRecordMapper;

    @Autowired
    WebSocketServer webSocketServer;

    @Autowired
    private LbCacheService lbCacheService;

    @Autowired
    private SpecifiedCountryMapper specifiedCountryMapper;

    // 使用 AtomicInteger 来维护当前的索引
    private final AtomicInteger currentIndex = new AtomicInteger(0);

    @Autowired
    private RecordAddressRepository recordAddressRepository;

    @Autowired
    private MongoTemplate mongoTemplate;
    @Resource
    private LumenService lumenService;
    @Resource
    private RedissonClient redissonClient;


    /**
     * 判断当前生图类型
     *
     * @param para
     * @return
     */
    public String judgeFeatures(GenGenericPara para) {
        String featuresResult = FeaturesType.ttp.getValue();
        if (!Objects.isNull(para)) {
            GenGenericPara.Multi_img2img_info multiImg2imgInfo = para.getMulti_img2img_info();
            if (!Objects.isNull(multiImg2imgInfo) && !CollectionUtils.isEmpty(multiImg2imgInfo.getStyle_list())) {
                if (multiImg2imgInfo.getStyle_list().size() > 1) {
                    featuresResult = FeaturesType.multiRefer.getValue();
                } else {
                    //判断是否是flux相关的模型
                    List<String> modelIds = Lists.newArrayList(artModelId, fluxschellModelId, fluxdevModelId);
                    Boolean beFlux = Boolean.FALSE;
                    if (modelIds.contains(para.getModel_id())) {
                        beFlux = Boolean.TRUE;
                    }

                    GenGenericPara.StyleImg styleImg = multiImg2imgInfo.getStyle_list().get(0);
                    switch (styleImg.getStyle()) {
                        case "contentRefer":
                            featuresResult = beFlux ? FeaturesType.ptpFluxContent.getValue() : FeaturesType.ptpContent.getValue();
                            break;
                        case "characterRefer":
                            //flux相关的模型用相关功能
                            featuresResult = beFlux ? FeaturesType.ptpFluxCharacter.getValue() : FeaturesType.ptpCharacter.getValue();
                            break;
                        case "styleRefer":
                            featuresResult = FeaturesType.ptpStyle.getValue();
                            break;
                        case "openposeControl":
                            featuresResult = FeaturesType.openposeControl.getValue();
                            break;
                        default:
                            featuresResult = FeaturesType.ttp.getValue();
                            break;
                    }
                }
            }
        }

        return featuresResult;
    }

    /**
     * 根据模型和功能，确定具体的队列名称
     *
     * @param modelId
     * @param feature
     * @return
     */
    private String[] getModelAndFeatureGroups(String modelId, String feature) {
        //根据模型id获取模型组
        String modelGroup = (String) redisService.getDataFromHash(LOADBALANCE_MODEL, modelId);
        modelGroup = StringUtil.isNotBlank(modelGroup) ? modelGroup : "m0";
        //根据功能获取功能组
        String featureGroup = (String) redisService.getDataFromHash(LOADBALANCE_FEATURE, feature);
        featureGroup = StringUtil.isNotBlank(featureGroup) ? featureGroup : "f0";
        //如果规则里找不到对应的信息，则将功能重新设置为f0
        String model_feature_rule = (String) redisService.getDataFromHash(LogicConstants.RULE_LIST_KEY, modelGroup + featureGroup);
        if (StringUtil.isBlank(model_feature_rule)) {
            featureGroup = "f0";
            String model_feature0_rule = (String) redisService.getDataFromHash(LogicConstants.RULE_LIST_KEY, modelGroup + featureGroup);
            if (StringUtil.isBlank(model_feature0_rule)) {
                modelGroup = "m0";
                featureGroup = "f0";
            }
        }
        return new String[]{modelGroup, featureGroup};
    }


    /**
     * 根据模型和功能，推送到不同的relax等待队列
     *
     * @param modelId
     * @param feature
     * @param markId
     * @param relaxWaitTime
     * @return
     */
    public String pushToRelaxWaitQueue(String modelId, String feature, String markId, int relaxWaitTime) {
        try {
            //根据模型id获取模型组
            String[] groups = getModelAndFeatureGroups(modelId, feature);
            String modelGroup = groups[0];
            String featureGroup = groups[1];
            if (StringUtil.isNotBlank(modelGroup) && StringUtil.isNotBlank(featureGroup)) {
                //判断当前任务时进入具体的等待队列
                String waitQueueKey = WAIT_QUEUE_NAME_PREFIX + modelGroup + featureGroup;
                try {
                    log.info("任务markId:{},进入relax等待队列，队列key为：{}，relax等待队列排队值：{}", markId, waitQueueKey,
                            redisService.getZsetSize(waitQueueKey));
                } catch (Exception e) {
                    log.error("获取size失败", e);
                }
                redisService.addTaskToDelayQueue(waitQueueKey, markId, Long.valueOf(relaxWaitTime));
                redisService.putDataToHash(MARK_ID_QUEUE_NAME, markId, waitQueueKey, 2, TimeUnit.HOURS);
                return waitQueueKey;
            }
        } catch (Exception e) {
            log.error("将生图任务taskId:{},推进等待队列，报错信息为：", markId, e);
        }
        return "";
    }


    /**
     * 根据模型和功能，以及判断ip和fastHour机制，推送到不同的队列
     *
     * @param modelId
     * @param feature
     * @param beUnFair
     * @param markId
     * @return
     */
    public String pushToTaskQueue(String modelId, String feature, Boolean fastHour, String markId, User user) {
        RLock lock = redissonClient.getLock(FAST_CREATE_LOCK_PREFIX + user.getLoginName());
        try {
            lock.lock();
            String[] groups = getModelAndFeatureGroups(modelId, feature);
            String modelGroup = groups[0];
            String featureGroup = groups[1];
            if (StringUtil.isNotBlank(modelGroup) && StringUtil.isNotBlank(featureGroup)) {
                //判断当前任务是否属于黑名单任务
                if (user.getBlackListFlag()) {
                    String blFluxQueueKey = BL_FLUX_QUEUE;
                    String blCommonQueueKey = BL_COMMON_QUEUE;
                    Boolean beFlux = (modelGroup + featureGroup).equalsIgnoreCase("m2f0");
                    if (beFlux) {
                        return BLFLUX_QUEUE_KEY_ERROR;
                    } else {
                        try {
                            Long blCommonQueueSize = redisService.listSize(blCommonQueueKey);
                            log.info("任务markId:{},进入黑名单Common队列，队列key为：{}，黑名单Common队列排队值：{}，黑名单Flux队列排队值：{}", markId, blCommonQueueKey,
                                    blCommonQueueSize, redisService.listSize(blFluxQueueKey));
                            if (blCommonQueueSize < 200) {
                                redisService.leftPush(blCommonQueueKey, markId);
                                redisService.putDataToHash(MARK_ID_QUEUE_NAME, markId, blCommonQueueKey, 2, TimeUnit.HOURS);
                                // 刷新用户未完成任务数及预扣点数
                                lumenService.notFinishTask(user.getLoginName());
                            } else {
                                return BLCOMMON_QUEUE_KEY_ERROR;
                            }
                        } catch (Exception e) {
                            log.error("pushToTaskQueue 入队失败", e);
                        }
                        return blCommonQueueKey;
                    }
                }

                //判断当前任务时进入公平队列还是非公平队列
                if (fastHour) {
                    String fairQueueKey = FAIR_QUEUE_NAME_PREFIX + modelGroup + featureGroup;
                    String unFairQueueKey = UNFAIR_QUEUE_NAME_PREFIX + modelGroup + featureGroup;
                    try {
                        log.info("任务markId:{},进入公平任务队列，队列key为：{}，公平队列排队值：{}，非公平队列排队值：{}", markId, fairQueueKey,
                                redisService.listSize(fairQueueKey), redisService.listSize(unFairQueueKey));
                    } catch (Exception e) {
                        log.error("获取size失败", e);
                    }
                    redisService.leftPush(fairQueueKey, markId);
                    redisService.putDataToHash(MARK_ID_QUEUE_NAME, markId, fairQueueKey, 2, TimeUnit.HOURS);
                    // 刷新用户未完成任务数及预扣点数
                    lumenService.notFinishTask(user.getLoginName());
                    return fairQueueKey;
                } else {
                    String fairQueueKey = FAIR_QUEUE_NAME_PREFIX + modelGroup + featureGroup;
                    String unFairQueueKey = UNFAIR_QUEUE_NAME_PREFIX + modelGroup + featureGroup;
                    try {
                        log.info("任务markId:{},进入非公平任务队列，队列key为：{}，公平队列排队值：{}，非公平队列排队值：{}", markId, unFairQueueKey,
                                redisService.listSize(fairQueueKey), redisService.listSize(unFairQueueKey));
                    } catch (Exception e) {
                        log.error("获取队列长度出错了", e);
                    }
                    redisService.leftPush(unFairQueueKey, markId);
                    redisService.putDataToHash(MARK_ID_QUEUE_NAME, markId, unFairQueueKey, 2, TimeUnit.HOURS);
                    return unFairQueueKey;
                }
            }
        } catch (Exception e) {
            log.error("将生图任务taskId:{},推进队列，报错信息为：", markId, e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return "";
    }

    /**
     * 判断当前用户是否是占用大量资源的非发达国家，目前是印度，巴基斯坦，印度尼西亚
     *
     * @return
     */
    public Boolean judgeTheLargeResourcesIpAddress(HttpServletRequest request) throws JsonProcessingException {
        try {
            String operateIp = IpUtils.getIpAddr(request);
            Boolean specifiedIpFlag = Boolean.FALSE;
            Set<Object> resultSet = redisService.getDataFromSet("TheLargeResourcesCountryFlag");
            //两小时后，从数据库重新查询最新的数据，进行同步
            if (CollectionUtils.isEmpty(resultSet)) {
                redisService.addDataToSet("TheLargeResourcesCountryFlag", countrySet);
                resultSet.addAll(countrySet);
            }
            specifiedIpFlag = resultSet.contains(geoIpCountryService.getCountry(operateIp).get());
            log.debug("当前ip: {},是否为印度，巴基斯坦，印度尼西亚等地区ip : {} ", operateIp, specifiedIpFlag);
            return specifiedIpFlag;
        } catch (Exception e) {
            log.error("判断用户是否为特定印度，巴基斯坦，印度尼西亚 等ip报错，报错信息为：{}", e.getMessage(), e);
            return Boolean.FALSE;
        }
    }


    /**
     * 判断当前用户是否是指定区域用户，目前是印度
     *
     * @param request
     * @return
     * @throws JsonProcessingException
     */
    public Boolean judgeTheIpAddress(HttpServletRequest request) throws JsonProcessingException {
        try {
            String operateIp = IpUtils.getIpAddr(request);
            Boolean specifiedIpFlag = Boolean.FALSE;
            String theSpecifiedCountryFlag = (String) redisService.get("SpecifiedCountryFlag");
            //两小时后，从数据库重新查询最新的数据，进行同步
            if (StringUtil.isBlank(theSpecifiedCountryFlag) || CollectionUtils.isEmpty(countrySet)) {
                LambdaUpdateWrapper<SpecifiedCountry> luw = new LambdaUpdateWrapper<>();
                luw.eq(SpecifiedCountry::getDel, 0);

                List<SpecifiedCountry> specifiedCountryList = specifiedCountryMapper.selectList(luw);
                if (!CollectionUtils.isEmpty(specifiedCountryList)) {
                    //清除旧有数据
                    countrySet.clear();
                    countrySet = specifiedCountryList.stream()
                            .map(SpecifiedCountry::getCountry)
                            .collect(Collectors.toSet());
                    //重新设置标记时间
                    redisService.set("SpecifiedCountryFlag", "flagValue", 2, TimeUnit.HOURS);
                }
            }
            specifiedIpFlag = countrySet.contains(geoIpCountryService.getCountry(operateIp).get());
            log.debug("当前ip: {},是否为印度等地区ip : {} ", operateIp, specifiedIpFlag);
            return specifiedIpFlag;
        } catch (Exception e) {
            log.error("判断用户是否为特定ip报错，报错信息为：{}", e.getMessage(), e);
            return Boolean.FALSE;
        }
    }

    /**
     * 判断当前用户是否已经超过fastHour时间
     *
     * @param loginName
     * @return
     */
//    public Boolean judgeTheFastHourExceede(String loginName) {
//        try {
//            Integer userCostTime = (Integer) redisService.getDataFromHash(USER_TASK_COST_TIMES, loginName);
//            if (!Objects.isNull(userCostTime) && userCostTime > fastHours) {
//                log.info("当前用户: {},为非fastHours生图模式", loginName);
//                return Boolean.TRUE;
//            }
//            log.info("当前用户: {},为fastHours生图模式", loginName);
//        } catch (Exception e) {
//            log.error("判断用户:{},是否已经超过fastHour机制报错，报错信息为：{}", loginName, e.getMessage(), e);
//            return Boolean.FALSE;
//        }
//        return Boolean.FALSE;
//    }

    /**
     * 调用py接口
     *
     * @return
     */
    public Map<String, Object> operateBackendApi(String markId, String addressId, String queueName) throws JsonProcessingException {
        Map<String, Object> resultMap = new HashMap<>();
        log.info("队列：{} 中的任务markId:{},被服务器:{}，所消费", queueName, markId, addressId);
        String userLoginName = redisService.stringGet(markId);

        //userLoginName 为空的则将任务设置为空
        if (StringUtil.isBlank(userLoginName)) {
            dealFailureTaskByEmptyLoginName(markId, "Task Failed");
        }

        //判断机器是否是全能模型
        Boolean allfeatureFlag = Boolean.TRUE;
        Object allfeature = redisService.getDataFromHash(ALL_FEATURE_SERVERIDS, addressId);
        if (!Objects.isNull(allfeature)) {
            allfeatureFlag = Boolean.FALSE;
        }

        //查询原始图片数据
        LambdaQueryWrapper<PromptRecord> qw = new LambdaQueryWrapper<PromptRecord>();
        qw.eq(PromptRecord::getMarkId, markId);
        qw.eq(PromptRecord::getLoginName, userLoginName);
        PromptRecord originPromptRecord = promptRecordMapper.selectOne(qw);

        if (Objects.isNull(originPromptRecord)) {
            log.error("查询不到对应任务的相关信息，图片markId：{}", markId);
            throw new OperationNotAllowedException("upscale failed !");
        }

        log.info("markId: {} 数据被消费", JsonUtils.writeToString(markId));
        try {
//            redisService.putDataToHash(userLoginName, markId, 0);
            redisService.putHashStrFieldIfExistsAndTTLValid(userLoginName, markId, 0);
            //通知websocket已经生图信息
            //sendMessageByWebsock(markId, userLoginName, 0, PromptStatus.running.getValue());
            //统计当天最大并发任务数
            staticTaskComplicated(redisService.listSize(queueName).intValue() + 1, queueName);

            //生图用户lumen任务数据+1
            mongoTemplate.updateFirst(
                    new Query(Criteria.where("userLoginName").is(userLoginName)),
                    new Update().inc("creationNums", 1),
                    TaskLumen.class
            );
        } catch (Exception e) {
            log.error("更新用户排队信息出错:{},任务:{}", userLoginName, markId, e);
        }

        try {
            String featureName = originPromptRecord.getFeatureName();
            BackendPromptResult backendPromptResult = new BackendPromptResult();


            if (StringUtil.isNotBlank(featureName)) {
                switch (featureName) {
                    case "removeBg":
                        backendPromptResult = invokeRemoveBg(originPromptRecord, addressId, allfeatureFlag);
                        break;
                    case "upscale":
                    case "enlargeUpscale":
                        backendPromptResult = invokeUpscale(originPromptRecord, addressId, allfeatureFlag);
                        break;
                    case "inpaint":
                        backendPromptResult = invokeInpaint(originPromptRecord, addressId, allfeatureFlag);
                        break;
                    case "expand":
                        backendPromptResult = invokeExpand(originPromptRecord, addressId, allfeatureFlag);
                        break;
                    case "lineRecolor":
                        backendPromptResult = invokeLineRecolor(originPromptRecord, addressId, allfeatureFlag);
                        break;
                    case "vary":
                        backendPromptResult = invokeVary(originPromptRecord, addressId, allfeatureFlag);
                        break;
                    default:
                        backendPromptResult = invokeGenCreatePicture(originPromptRecord, addressId, allfeatureFlag);
                        break;
                }
            }

            if (200 == backendPromptResult.getCode() && !Objects.isNull(backendPromptResult.getResult())) {
                BackendPromptResult.Result result = backendPromptResult.getResult();

                resultMap.put("promptId", result.getPrompt_id());
                resultMap.put("taskId", result.getTask_id());
                resultMap.put("number", result.getNumber());
                resultMap.put("markId", result.getMark_id());

                //将生图的promptId进行入库
                LambdaUpdateWrapper<PromptRecord> luw = new LambdaUpdateWrapper<PromptRecord>();
                luw.eq(PromptRecord::getMarkId, result.getMark_id());
                luw.eq(PromptRecord::getLoginName, userLoginName);
                luw.set(PromptRecord::getPromptId, result.getPrompt_id());
                luw.set(PromptRecord::getTaskId, result.getTask_id());
                luw.set(PromptRecord::getTaskNumber, result.getNumber());
                luw.set(PromptRecord::getGenStartTime, LocalDateTime.now());
                promptRecordMapper.update(null, luw);

                redisService.set(result.getPrompt_id(), result.getMark_id(), 5, TimeUnit.MINUTES);
                //更新用户的生图时间戳
                redisService.set(USER_TASK_TIMESTAMP + markId, System.currentTimeMillis(), 2, TimeUnit.HOURS);
            } else {
                log.error("删除对应失败的任务，markId的：{},用户名称为：{}", markId, userLoginName);
                dealFailureTask(markId, "", userLoginName, "Task Failed");
            }
        } catch (Exception e) {
            log.error("删除对应失败的任务，markId的：{},用户名称为：{}", markId, userLoginName, e);
            dealFailureTask(markId, "", userLoginName, "Task Failed");
        }

        try {
            //图片的生图地址存到MongoDB
            RecordAddress recordAddress = new RecordAddress();
            recordAddress.setMarkId(markId);
            recordAddress.setAddressId(addressId);
            recordAddress.setPromptId(String.valueOf(resultMap.get("promptId")));
            recordAddress.setFastHour(originPromptRecord.getFastHour());
            recordAddress.setLoginName(userLoginName);
            recordAddress.setServerIp(IpUtils.getPublicIP());
            recordAddress.setOriginCreate(originPromptRecord.getOriginCreate());
            recordAddress.setModelId(originPromptRecord.getModelId());
            recordAddressRepository.insert(recordAddress);
        } catch (Exception e) {
            log.error("图片的生图地址存到MongoDB失败，markId:{}", markId, e);
        }

        return resultMap;
    }

    public void dealFailureTaskByEmptyLoginName(String markId, String errorMessage) {
        //将任务进行删除
        LambdaUpdateWrapper<PromptRecord> luw = new LambdaUpdateWrapper<PromptRecord>();
        luw.eq(PromptRecord::getMarkId, markId);
        luw.eq(PromptRecord::getDel, Boolean.FALSE);
        luw.set(PromptRecord::getDel, Boolean.TRUE);
        luw.set(PromptRecord::getUpdateTime, LocalDateTime.now());
        String failureMessage = "";
        if (StringUtil.isNotBlank(errorMessage)) {
            luw.set(PromptRecord::getFailureMessage, failureMessage);
        }
        promptRecordMapper.update(null, luw);

        //移除markId对应的队列名称
        redisService.deleteFieldFromHash(MARK_ID_QUEUE_NAME, markId);
    }

    public void dealFailureTask(String markId, String promptId, String userLoginName, String errorMessage) throws JsonProcessingException {
        log.info("删除用户：{}，对应失败的任务，markId:{}, errorMessage: {}", userLoginName, markId, errorMessage);
        try {
            redisService.deleteFieldFromHash(userLoginName, markId);

            String serverId = (String) redisService.getDataFromHash(LogicConstants.MARKID_SERVERID_LIST, markId);
            if (redisService.listRemoveValue(serverId, markId) > 0) {
                redisService.comfySizeDecr(LogicConstants.COMFY_SIZE_PREFIX + serverId);
            }

            //移除markId对应的队列名称
            redisService.deleteFieldFromHash(MARK_ID_QUEUE_NAME, markId);

            redisService.delete(USER_TASK_TIMESTAMP + markId);

            redisService.delete(promptId);
        } catch (Exception e) {
            log.error("dealFailureTask error", e);
        }

        //查询生图信息
        LambdaQueryWrapper<PromptRecord> lqw = new LambdaQueryWrapper<>();
        lqw.eq(PromptRecord::getMarkId, markId);
        if (StringUtil.isNotBlank(userLoginName)) {
            lqw.eq(PromptRecord::getLoginName, userLoginName);
        }
        lqw.eq(PromptRecord::getDel, Boolean.FALSE);
        PromptRecord promptRecord = promptRecordMapper.selectOne(lqw);
        Boolean fastHour = null;
        if (promptRecord != null) {
            fastHour = promptRecord.getFastHour();
            if (StringUtil.isBlank(userLoginName)) {
                userLoginName = promptRecord.getLoginName();
            }
        }

        //修改任务状态为失败
        LambdaUpdateWrapper<PromptRecord> luw = new LambdaUpdateWrapper<PromptRecord>();
        luw.eq(PromptRecord::getMarkId, markId);
        if (StringUtil.isNotBlank(userLoginName)) {
            luw.eq(PromptRecord::getLoginName, userLoginName);
        }
        luw.eq(PromptRecord::getDel, Boolean.FALSE);
        luw.set(PromptRecord::getDel, Boolean.TRUE);
        luw.set(PromptRecord::getUpdateTime, LocalDateTime.now());
        luw.set(PromptRecord::getUpdateBy, userLoginName);
        String failureMessage = "";
        if (StringUtil.isNotBlank(errorMessage)) {
            failureMessage = errorMessage.replace(promptId, "").trim();
            luw.set(PromptRecord::getFailureMessage, failureMessage);
        }
        promptRecordMapper.update(null, luw);

        //刷新未完成的任务点数
        if (Objects.equals(fastHour, Boolean.TRUE) && StringUtil.isNotBlank(userLoginName)) {
            lumenService.notFinishTask(userLoginName);
        }
    }


    public void senQueueIndex() {
        List<String> queueList = redisService.getAllKeysFromHash(LogicConstants.RULE_LIST_KEY);
        if (!CollectionUtils.isEmpty(queueList)) {
            for (String queueName : queueList) {
                String fairQueueName = LogicConstants.FAIR_QUEUE_NAME_PREFIX + queueName;
                String unfairQueueName = LogicConstants.UNFAIR_QUEUE_NAME_PREFIX + queueName;
                try {
                    dealProcess(fairQueueName, 0);
                } catch (Exception e) {
                    log.error("队列：{} 发送排队报错", fairQueueName, e);
                }
                try {
                    Integer fairTaskThreshold = (lbCacheService.getFromCacheOrRedis(LogicConstants.FAIR_TASK_THRESHOLD, 7));
                    Integer unfairTaskThreshold = (lbCacheService.getFromCacheOrRedis(LogicConstants.UNFAIR_TASK_THRESHOLD, 3));
                    Integer proportion = (int) Math.floor(fairTaskThreshold / unfairTaskThreshold);
                    dealProcess(unfairQueueName, proportion);
                } catch (Exception e) {
                    log.error("队列：{} 发送排队报错", unfairQueueName, e);
                }
                try {
                    dealProcess(BL_FLUX_QUEUE, 0);
                } catch (Exception e) {
                    log.error("队列：{} 发送排队报错", BL_FLUX_QUEUE, e);
                }
                try {
                    dealProcess(BL_COMMON_QUEUE, 0);
                } catch (Exception e) {
                    log.error("队列：{} 发送排队报错", BL_COMMON_QUEUE, e);
                }
            }
        }
    }


    public void sendRelaxWaitQueue() {
        List<String> queueList = redisService.getAllKeysFromHash(LogicConstants.RULE_LIST_KEY);
        if (!CollectionUtils.isEmpty(queueList)) {
            for (String queueName : queueList) {
                String waitQueueName = LogicConstants.WAIT_QUEUE_NAME_PREFIX + queueName;
                try {
                    List<String> resultList = redisService.popExpiredTasksFromDelayQueue(waitQueueName);
                    if (!CollectionUtils.isEmpty(resultList)) {
                        String unFairQueueKey = "unfairQueue_" + queueName;
                        redisService.leftPushAllJustForWait(unFairQueueKey, resultList);
                    }
                } catch (Exception e) {
                    log.error("relax等待队列：{} 发送到非公平报错", waitQueueName, e);
                }
            }
        }
    }


    /**
     * 将排队信息发送给前端
     *
     * @param queueName
     * @return
     * @throws JsonProcessingException
     */
    public Boolean dealProcess(String queueName, Integer unfairLine) {
        // 获取对应的队列信息
        return addQueueIndex(queueName, unfairLine);
    }

    private Boolean addQueueIndex(String queueName, Integer unfairLine) {
        List<Object> taskQueueKey = redisService.getList(queueName);

        if (!CollectionUtils.isEmpty(taskQueueKey)) {
            // 进行反转
            Collections.reverse(taskQueueKey);
            int batchSize = 10;
            List<List<Object>> partitions = Lists.partition(taskQueueKey, batchSize);
            for (int i = 0; i < partitions.size(); i++) {
                List<Object> list = partitions.get(i);
                final Integer lastIndex = i * batchSize;
                // 使用Redis的pipeline批量处理
                redisService.executePipelined(connection -> {
                    for (int j = 0; j < list.size(); j++) {
                        // 将 i 和 taskMarkId 定义为局部变量，确保是 effectively final
                        final String taskMarkId = (String) list.get(j);
                        final Integer currentTaskIndex = (unfairLine > 0) ? ((lastIndex + j + 1) * unfairLine) : (lastIndex + j + 1);
                        final String taskUserLoginName = redisService.stringGet(taskMarkId);

                        // 批量写入Redis中的hash数据
                        try {
                            if (StringUtil.isNotBlank(taskMarkId) && !Objects.isNull(currentTaskIndex) && StringUtil.isNotBlank(taskUserLoginName)) {
//                                connection.hSet(
//                                        redisService.serializeKey(taskUserLoginName),
//                                        redisService.serializeHashKey(taskMarkId),
//                                        redisService.serializeHashValue(currentTaskIndex)
//                                );
                                // 使用 Lua 脚本执行 HSET
                                connection.eval(
                                        // Lua 脚本
                                        LUA_SCRIPT_SET_HASH_VALUE.getBytes(StandardCharsets.UTF_8),
                                        // 返回类型
                                        ReturnType.INTEGER,
                                        // KEYS 的数量
                                        1,
                                        // KEYS 和 ARGV 的序列化值
                                        redisService.serializeKey(taskUserLoginName), redisService.serializeHashKey(taskMarkId), redisService.serializeHashValue(currentTaskIndex));
                            }
                        } catch (JsonProcessingException e) {
                            throw new RuntimeException(e);
                        }
                    }
                    return null;
                });
            }
        }
        return Boolean.TRUE;
    }


    /**
     * 统计当天最大并发任务数以及单个队列最大任务数
     */
    @Async
    public void staticTaskComplicated(Integer maxTaskSize, String queueName) {
        String nowDate = DateUtils.dateTime();
        String key = "dayMaxTaskSize";
        Double currentScore = (Double) redisService.getDataFromZset(key, nowDate);
        if (currentScore == null || maxTaskSize > currentScore) {
            redisService.addDataToZset(key, nowDate, Double.valueOf(maxTaskSize));
        }

        String queuekey = "dayMaxTaskSizeList:" + queueName;
        Double queueCurrentScore = (Double) redisService.getDataFromZset(queuekey, nowDate);

        if (queueCurrentScore == null || maxTaskSize > queueCurrentScore) {
            redisService.addDataToZset(queuekey, nowDate, Double.valueOf(maxTaskSize));
        }
    }


    /**
     * 通过websocket发送消息
     *
     * @param markId
     * @param userLoginName
     * @param index
     * @param promptStatus
     * @throws JsonProcessingException
     */
    public void sendMessageByWebsock(String markId, String userLoginName, int index, String promptStatus) throws JsonProcessingException {
        //初始化发给websocket消息
        List<WsCallBackMessage.Message> messageList = new ArrayList<>();
        WsCallBackMessage wsCallBackMessage = new WsCallBackMessage();
        WsCallBackMessage.Message message = new WsCallBackMessage.Message();

        //设置websocket回调消息
        message.setMarkId(markId);
        message.setStatus(promptStatus);
        message.setIndex(index);
        messageList.add(message);

        //数据发送给websocket
        wsCallBackMessage.setMessage(messageList);
        //将图片信息通过websocket传给前端
        if (StringUtil.isNotBlank(userLoginName)) {
            log.info("给用户：{}，发送websocket消息:{}", userLoginName, JsonUtils.writeToString(wsCallBackMessage));
            webSocketServer.sendToOne(userLoginName, JsonUtils.writeToString(wsCallBackMessage));
        }
    }

    /**
     * 调用python去背景接口
     *
     * @param originPromptRecord
     * @return
     * @throws JsonProcessingException
     */
    public BackendPromptResult invokeRemoveBg(PromptRecord originPromptRecord, String addressId, Boolean allfeatureFlag) throws JsonProcessingException {

        RembgParams rembgParams = JsonUtils.fromJsonNode(originPromptRecord.getPromptParams(), RembgParams.class);
        rembgParams.setAddress(addressId);
        rembgParams.setAccelerated(allfeatureFlag);

        log.info("去背景请求参数: {}", JsonUtils.writeToString(rembgParams));
        Response<BackendPromptResult> responseBody = backendApi.rembgPicture(rembgParams);
        log.info("调用去背景返回信息: {}", JsonUtils.writeToString(responseBody.body()));

        // 获取响应数据
        BackendPromptResult backendPromptResult = responseBody.body();

        return backendPromptResult;
    }

    /**
     * 调用python高清修复接口
     *
     * @param originPromptRecord
     * @return
     * @throws JsonProcessingException
     */
    public BackendPromptResult invokeUpscale(PromptRecord originPromptRecord, String addressId, Boolean allfeatureFlag) throws JsonProcessingException {
        //判断参数是否是非上传图片的超分
        boolean isUpscaleParams = FeaturesType.enlargeUpscale.getValue().equals(originPromptRecord.getFeatureName());
        Response<BackendPromptResult> responseBody;
        //非上传的图片和上传的图片走不同的接口
        if (!isUpscaleParams) {
            HiresFixParams hiresFixParams = JsonUtils.fromJsonNode(originPromptRecord.getPromptParams(), HiresFixParams.class);
            hiresFixParams.setAddress(addressId);
            hiresFixParams.setAccelerated(allfeatureFlag);

            log.info("高清修复请求参数: {}", JsonUtils.writeToString(hiresFixParams));
            responseBody = backendApi.fixPicture(hiresFixParams);
            log.info("调用高清修复返回信息: {}", JsonUtils.writeToString(responseBody.body()));
        } else {
            UpscaleParams upscaleParams = JsonUtils.fromJsonNode(originPromptRecord.getPromptParams(), UpscaleParams.class);
            upscaleParams.setAddress(addressId);
            upscaleParams.setAccelerated(allfeatureFlag);

            log.info("上传图片高清修复请求参数: {}", JsonUtils.writeToString(upscaleParams));
            responseBody = backendApi.upscale(upscaleParams);
            log.info("调用上传图片高清修复返回信息: {}", JsonUtils.writeToString(responseBody.body()));
        }

        // 获取响应数据
        BackendPromptResult backendPromptResult = responseBody.body();

        return backendPromptResult;
    }

    /**
     * 调用python局部重绘接口
     *
     * @param originPromptRecord
     * @return
     * @throws JsonProcessingException
     */
    public BackendPromptResult invokeInpaint(PromptRecord originPromptRecord, String addressId, Boolean allfeatureFlag) throws JsonProcessingException {

        LocalRedrawPromptParams localRedrawParams = JsonUtils.fromJsonNode(originPromptRecord.getPromptParams(), LocalRedrawPromptParams.class);
        localRedrawParams.setAddress(addressId);
        localRedrawParams.setAccelerated(allfeatureFlag);

        log.info("局部重绘请求参数: {}", JsonUtils.writeToString(localRedrawParams));
        Response<BackendPromptResult> responseBody = backendApi.localRedraw(localRedrawParams);
        log.info("调用局部重绘返回信息: {}", JsonUtils.writeToString(responseBody.body()));

        // 获取响应数据
        BackendPromptResult backendPromptResult = responseBody.body();

        return backendPromptResult;
    }

    /**
     * 调用python扩图接口
     *
     * @param originPromptRecord
     * @return
     * @throws JsonProcessingException
     */
    public BackendPromptResult invokeExpand(PromptRecord originPromptRecord, String addressId, Boolean allfeatureFlag) throws JsonProcessingException {

        EnlargeImageParams enlargeImageParams = JsonUtils.fromJsonNode(originPromptRecord.getPromptParams(), EnlargeImageParams.class);
        enlargeImageParams.setAddress(addressId);
        enlargeImageParams.setAccelerated(allfeatureFlag);

        log.info("扩图请求参数: {}", JsonUtils.writeToString(enlargeImageParams));
        Response<BackendPromptResult> responseBody = backendApi.enlargeImage(enlargeImageParams);
        log.info("调用扩图返回信息: {}", JsonUtils.writeToString(responseBody.body()));

        // 获取响应数据
        BackendPromptResult backendPromptResult = responseBody.body();

        return backendPromptResult;
    }

    /**
     * 调用python线稿上色接口
     *
     * @param originPromptRecord
     * @return
     * @throws JsonProcessingException
     */
    public BackendPromptResult invokeLineRecolor(PromptRecord originPromptRecord, String addressId, Boolean allfeatureFlag) throws JsonProcessingException {

        LineRecolorParams lineRecolorParams = JsonUtils.fromJsonNode(originPromptRecord.getPromptParams(), LineRecolorParams.class);
        lineRecolorParams.setAddress(addressId);
        lineRecolorParams.setAccelerated(allfeatureFlag);

        log.info("线稿上色请求参数: {}", JsonUtils.writeToString(lineRecolorParams));
        Response<BackendPromptResult> responseBody = backendApi.lineRecolor(lineRecolorParams);
        log.info("调用线稿上色返回信息: {}", JsonUtils.writeToString(responseBody.body()));

        // 获取响应数据
        BackendPromptResult backendPromptResult = responseBody.body();

        return backendPromptResult;
    }

    /**
     * 调用python图片微变
     *
     * @param originPromptRecord
     * @return
     * @throws JsonProcessingException
     */
    public BackendPromptResult invokeVary(PromptRecord originPromptRecord, String addressId, Boolean allfeatureFlag) throws JsonProcessingException {

        VaryParams varyParams = JsonUtils.fromJsonNode(originPromptRecord.getPromptParams(), VaryParams.class);
        varyParams.setAddress(addressId);
        varyParams.setAccelerated(allfeatureFlag);

        log.info("图片微变请求参数: {}", JsonUtils.writeToString(varyParams));
        Response<BackendPromptResult> responseBody = backendApi.vary(varyParams);
        log.info("调用图片微变返回信息: {}", JsonUtils.writeToString(responseBody.body()));

        // 获取响应数据
        BackendPromptResult backendPromptResult = responseBody.body();

        return backendPromptResult;
    }

    /**
     * 调用python生图接口
     *
     * @param originPromptRecord
     * @return
     * @throws JsonProcessingException
     */
    public BackendPromptResult invokeGenCreatePicture(PromptRecord originPromptRecord, String addressId, Boolean allfeatureFlag) throws JsonProcessingException {

        BackendPromptParams requestParam = JsonUtils.fromJsonNode(originPromptRecord.getPromptParams(), BackendPromptParams.class);
        requestParam.setAddress(addressId);
        requestParam.setAccelerated(allfeatureFlag);

        log.info("backend  Prompt 请求参数: {}", JsonUtils.writeToString(requestParam));
        Response<BackendPromptResult> responseBody = backendApi.createPicture(requestParam);
        log.info("调用Backend后端返回信息: {}", JsonUtils.writeToString(responseBody.body()));

        // 获取响应数据
        BackendPromptResult backendPromptResult = responseBody.body();

        return backendPromptResult;
    }


    public List<ModelInformation.ModelAbout> listModels() throws IOException {

        String modelDataKey = "modelListKey";
        List<ModelInformation.ModelAbout> modelAboutList = new ArrayList<>();

        /**
         * 如果redis中有数据，则直接返回
         */
        String modelListResult = (String) redisService.get(modelDataKey);
        if (StringUtil.isNotBlank(modelListResult)) {
            modelAboutList.addAll(JsonUtils.writeToList(modelListResult, ModelInformation.ModelAbout.class));
            return modelAboutList;
        }

        List<ModelInformation.ModelAbout> modelAbouts = modelService.buildModelList(null);
//        Response<ModelInformation> responseBody = backendApi.getModelInformation();
//        log.info("调用Backend后端获取模型列表返回返回信息: {}", JsonUtils.writeToString(responseBody.body()));

        //调用py后端查询相关信息
        if (!CollectionUtils.isEmpty(modelAbouts)) {
            redisService.set(modelDataKey, JsonUtils.writeToString(modelAbouts), 1, TimeUnit.HOURS);
            modelAboutList.addAll(modelAbouts);

            //查询模型相关的信息
            Map<String, ModelInformation.ModelAbout> modelAboutMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(modelAboutList)) {
                modelAboutMap = modelAboutList.stream().collect(Collectors.toMap(ModelInformation.ModelAbout::getModelId, Function.identity()));
            }
            ModelInformation.ModelAbout realisticModelAbout = modelAboutMap.get(realisticModelId);
            if (!Objects.isNull(realisticModelAbout)) {
                redisService.set(REALISTIC_MODEL_KEY, JsonUtils.writeToString(realisticModelAbout.getDefaultConfig()));
            }
            ModelInformation.ModelAbout animeModelAbout = modelAboutMap.get(animeModelId);
            if (!Objects.isNull(animeModelAbout)) {
                redisService.set(ANIME_MODEL_KEY, JsonUtils.writeToString(animeModelAbout.getDefaultConfig()));
            }
            ModelInformation.ModelAbout lineartModelAbout = modelAboutMap.get(lineartModelId);
            if (!Objects.isNull(lineartModelAbout)) {
                redisService.set(LINEART_MODEL_KEY, JsonUtils.writeToString(lineartModelAbout.getDefaultConfig()));
            }
        }

        return modelAboutList;
    }

    // 生成元数据字符串
    private String generateParameters(PromptRecord promptRecord) throws IOException {
        // 使用 StringBuilder 代替字符串拼接
        StringBuilder parameters = new StringBuilder();

        // 基本参数拼接
        if (!StringUtils.isNull(promptRecord.getPrompt())) {
            parameters.append(promptRecord.getPrompt());
        }
        parameters.append(CommandPromptParameters.PICLUMEN)
                .append(CommandPromptParameters.ASPECT)
                .append(promptRecord.getAspectRatio()
                        .replace(" * ", ":"));

        // 处理 negative prompt
        if (!StringUtils.isNull(promptRecord.getNegativePrompt())) {
            parameters.append(CommandPromptParameters.NEGATIVE_PROMPT).append("\"").append(promptRecord.getNegativePrompt()).append("\"");
        } else {
            parameters.append(CommandPromptParameters.NEGATIVE_PROMPT).append("\"\"");
        }

        // 处理 genInfo 参数
        GenGenericPara genGenericPara = JsonUtils.fromJsonNode(promptRecord.getGenInfo(), GenGenericPara.class);

        //判断生成信息是否为空 不为空 则写入相应的信息进入元数据
        if (!Objects.isNull(genGenericPara)) {

            if (!Objects.isNull(genGenericPara.getCfg())) {
                parameters.append(CommandPromptParameters.GUIDANCE).append(genGenericPara.getCfg());
            }

            if (!Objects.isNull(genGenericPara.getSteps())) {
                parameters.append(CommandPromptParameters.STEPS).append(genGenericPara.getSteps());
            }

            if (!Objects.isNull(genGenericPara.getSeed())) {
                parameters.append(CommandPromptParameters.SEED).append(genGenericPara.getSeed());
            }
        }

        // 添加批量大小
        parameters.append(CommandPromptParameters.BATCH_SIZE).append(promptRecord.getBatchSize());

        // 处理模型信息
        List<ModelInformation.ModelAbout> modelAbouts = listModels();
        for (ModelInformation.ModelAbout model : modelAbouts) {
            if (model.getModelId().equals(promptRecord.getModelId())) {
                parameters.append(CommandPromptParameters.MODEL_NAME).append("\"").append(model.getModelDisplay()).append("\"");
                break;
            }
        }

        // 添加生图类型
        parameters.append(CommandPromptParameters.ORIGIN_CREATE).append("\"").append(promptRecord.getOriginCreate()).append("\"");

        // 返回字符串
        return parameters.toString();
    }


    public int getLargeResourceCountryWaitTime(int imageCount) {
        try {
            TreeMap<Integer, Integer> waitTimeMap = countryDelayTimeCache.get(COUNTRY_DELAY_TIMES_KEY, () -> {
                // 从 Redis 获取数据
                Map<Object, Object> redisData = redisService.getHashAsMap(COUNTRY_DELAY_TIMES_KEY);

                if (redisData == null || redisData.isEmpty()) {
                    // 如果 Redis 也没有数据，则使用本地配置
                    TreeMap<Integer, Integer> initialMap = new TreeMap<>();
                    for (int[] entry : NOT_VIP_DELAY_TIMES) {
                        initialMap.put(entry[0], entry[1]);
                    }

                    // 将 TreeMap 转换为 Map<String, Object> 类型
                    Map<String, Object> redisMap = new HashMap<>();
                    for (Map.Entry<Integer, Integer> entry : initialMap.entrySet()) {
                        redisMap.put(entry.getKey().toString(), entry.getValue());
                    }

                    // 存入 Redis
                    redisService.putAllObjcetToHash(COUNTRY_DELAY_TIMES_KEY, redisMap);
                    return initialMap;  // 直接返回初始化数据
                }

                // 转换 Redis 返回的数据为 TreeMap<Integer, Integer>
                return redisData.entrySet().stream()
                        .collect(Collectors.toMap(
                                e -> Integer.parseInt(e.getKey().toString()),  // 统一转换 key 为 Integer
                                e -> Integer.parseInt(e.getValue().toString()),  // 处理 Object 转 Integer
                                (oldValue, newValue) -> newValue,
                                TreeMap::new
                        ));
            });

            // 获取小于等于 imageCount 的最近键值对
            Map.Entry<Integer, Integer> entry = waitTimeMap.floorEntry(imageCount);
            return (entry != null) ? entry.getValue() : 0;
        } catch (ExecutionException e) {
            log.error("Failed to load country delay times from cache or Redis", e);
            return 0;
        }
    }

    /**
     * 根据国家和生图数量获取等待时间（秒）
     *
     * @param aboveThaiGDP 是否超越泰国GDP
     * @param imageCount   生图数量
     * @return 等待时间（秒）
     */
    public int getWaitTime(Boolean aboveThaiGDP, int imageCount) {
        int[][] delayTimes = cache.getIfPresent(LogicConstants.DELAY_TIMES_KEY);

        // 如果缓存为空或者缓存过期，从 Redis 获取并刷新缓存
        if (delayTimes == null) {
            refreshCache();
            delayTimes = cache.getIfPresent(LogicConstants.DELAY_TIMES_KEY);
        }
        if (delayTimes == null) {
            delayTimes = LogicConstants.DELAY_TIMES;
        }

        // 如果生图数量小于第一个区间的起始值，直接返回 0
        if (imageCount < delayTimes[0][0]) {
            return 0;
        }

        for (int i = 0; i < delayTimes.length; i++) {
            int[] row = delayTimes[i];

            // 判断生图数量是否在当前区间
            if (imageCount >= row[0] && (i + 1 < delayTimes.length ? imageCount < delayTimes[i + 1][0] : true)) {
                return aboveThaiGDP ? row[1] : row[2];
            }
        }

        // 如果生图数量超过最大值，返回最后一个区间的等待时间
        int[] lastRow = delayTimes[delayTimes.length - 1];
        return aboveThaiGDP ? lastRow[1] : lastRow[2];
    }

    // 刷新缓存的数据（从 Redis 获取 DELAY_TIMES 配置）
    private void refreshCache() {
        try {
            // 从 Redis 获取序列化后的延迟时间配置
            String cachedData = redisService.stringGet(LogicConstants.DELAY_TIMES_KEY);

            // 如果 Redis 中没有数据，则使用默认值
            if (cachedData == null) {
                cachedData = objectMapper.writeValueAsString(LogicConstants.DELAY_TIMES);
            }

            int[][] delayTimes = objectMapper.readValue(cachedData, int[][].class);

            cache.put(LogicConstants.DELAY_TIMES_KEY, delayTimes);

            redisService.stringSet(LogicConstants.DELAY_TIMES_KEY, cachedData);

        } catch (IOException e) {
            //异常不处理，后续会直接使用默认值
        }
    }

}
