package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lx.pl.client.FluxKontextApiClient;
import com.lx.pl.config.FluxKontextConfig;
import com.lx.pl.db.mysql.gen.entity.PromptRecord;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.fluxkontext.FluxKontextRequest;
import com.lx.pl.dto.fluxkontext.FluxKontextResponse;
import com.lx.pl.enums.FluxKontextTaskStatus;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.enums.OriginCreate;
import com.lx.pl.exception.LogicException;
import com.lx.pl.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;

import java.time.LocalDateTime;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Flux Kontext Pro服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FluxKontextService {

    private static final String FLUX_TASK_PREFIX = "flux_kontext_task:";
    private static final String FLUX_USER_TASK_PREFIX = "flux_kontext_user_tasks:";

    @Autowired
    private FluxKontextConfig fluxKontextConfig;

    @Autowired
    private FluxKontextApiClient fluxKontextApiClient;

    @Autowired
    private PromptRecordMapper promptRecordMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private PromptFiltrationService promptFiltrationService;

    @Autowired
    private VipService vipService;

    @Autowired
    private FluxKontextCallbackService fluxKontextCallbackService;

    /**
     * 文本生图
     */
    public FluxKontextResponse.BaseResponseData textToImage(String prompt, User user,
                                                            String aspectRatio, Integer seed,
                                                            boolean enablePromptCheck) {
        return textToImage(prompt, user, aspectRatio, seed, enablePromptCheck, null, null, null, null);
    }

    /**
     * 文本生图 (完整版本，支持PromptRecord入库)
     */
    public FluxKontextResponse.BaseResponseData textToImage(String prompt, User user,
                                                            String aspectRatio, Integer seed,
                                                            boolean enablePromptCheck, String markId,
                                                            Boolean fastHour, String platform,
                                                            GenGenericPara genParameters) {
        try {
            // Prompt检查
            if (enablePromptCheck) {
                Boolean filterChildSex = promptFiltrationService.filterChildSex(prompt);
                if (filterChildSex) {
                    throw new LogicException(LogicErrorCode.ILLEGAL_PROMPT);
                }
            }

            // 生成任务ID
            String taskId = UUID.randomUUID().toString();
            if (StringUtil.isBlank(markId)) {
                markId = taskId;
            }

            // 构建请求
            FluxKontextRequest.TextToImageRequest request = new FluxKontextRequest.TextToImageRequest();
            request.setPrompt(prompt);
            request.setAspectRatio(StringUtil.isNotBlank(aspectRatio) ? aspectRatio : "1:1");
            request.setSeed(seed);
            request.setSafetyTolerance(fluxKontextConfig.getDefaultSafetyTolerance());
            request.setOutputFormat(fluxKontextConfig.getDefaultOutputFormat());
            request.setWebhookUrl(fluxKontextConfig.getCallbackUrl());

            log.info("Flux Kontext text-to-image request: {}", JsonUtils.writeToString(request));

            // 调用API
            Call<FluxKontextResponse.BaseResponseData> call = fluxKontextApiClient.createTask(
                    fluxKontextConfig.getApiKey(), request);
            Response<FluxKontextResponse.BaseResponseData> response = call.execute();

            if (!response.isSuccessful() || response.body() == null) {
                log.error("Flux Kontext API call failed: {}", response.errorBody() != null ?
                        response.errorBody().string() : "Unknown error");
                throw new LogicException(LogicErrorCode.THIRD_PARTY_API_ERROR);
            }

            FluxKontextResponse.BaseResponseData result = response.body();
            String requestId = result.getId();

            // 保存任务信息到Redis
            saveTaskToRedis(requestId, user.getLoginName(), markId);

            // 保存到数据库
            savePromptRecord(requestId, prompt, user, aspectRatio, seed, markId, fastHour,
                    platform, genParameters, request, OriginCreate.fluxKontextTextToImage.getValue());

            // 异步轮询任务状态
            startPollingTask(requestId, user.getLoginName());

            return result;

        } catch (Exception e) {
            log.error("Flux Kontext text-to-image failed", e);
            if (e instanceof LogicException) {
                throw e;
            }
            throw new LogicException(LogicErrorCode.THIRD_PARTY_API_ERROR);
        }
    }

    /**
     * 图像编辑
     */
    public FluxKontextResponse.BaseResponseData imageEdit(String prompt, String inputImageBase64,
                                                          User user, String aspectRatio, Integer seed,
                                                          boolean enablePromptCheck) {
        return imageEdit(prompt, inputImageBase64, user, aspectRatio, seed, enablePromptCheck,
                null, null, null, null);
    }

    /**
     * 图像编辑 (完整版本)
     */
    public FluxKontextResponse.BaseResponseData imageEdit(String prompt, String inputImageBase64,
                                                          User user, String aspectRatio, Integer seed,
                                                          boolean enablePromptCheck, String markId,
                                                          Boolean fastHour, String platform,
                                                          GenGenericPara genParameters) {
        try {
            // Prompt检查
            if (enablePromptCheck) {
                Boolean filterChildSex = promptFiltrationService.filterChildSex(prompt);
                if (filterChildSex) {
                    throw new LogicException(LogicErrorCode.ILLEGAL_PROMPT);
                }
            }

            // 生成任务ID
            String taskId = UUID.randomUUID().toString();
            if (StringUtil.isBlank(markId)) {
                markId = taskId;
            }

            // 构建请求
            FluxKontextRequest.ImageEditRequest request = new FluxKontextRequest.ImageEditRequest();
            request.setPrompt(prompt);
            request.setInputImage(inputImageBase64);
            request.setAspectRatio(StringUtil.isNotBlank(aspectRatio) ? aspectRatio : "1:1");
            request.setSeed(seed);
            request.setSafetyTolerance(fluxKontextConfig.getDefaultSafetyTolerance());
            request.setOutputFormat(fluxKontextConfig.getDefaultOutputFormat());
            request.setWebhookUrl(fluxKontextConfig.getCallbackUrl());

            log.info("Flux Kontext image-edit request: {}", JsonUtils.writeToString(request));

            // 调用API
            Call<FluxKontextResponse.BaseResponseData> call = fluxKontextApiClient.createTask(
                    fluxKontextConfig.getApiKey(), request);
            Response<FluxKontextResponse.BaseResponseData> response = call.execute();

            if (!response.isSuccessful() || response.body() == null) {
                log.error("Flux Kontext API call failed: {}", response.errorBody() != null ?
                        response.errorBody().string() : "Unknown error");
                throw new LogicException(LogicErrorCode.THIRD_PARTY_API_ERROR);
            }

            FluxKontextResponse.BaseResponseData result = response.body();
            String requestId = result.getId();

            // 保存任务信息到Redis
            saveTaskToRedis(requestId, user.getLoginName(), markId);

            // 保存到数据库
            savePromptRecord(requestId, prompt, user, aspectRatio, seed, markId, fastHour,
                    platform, genParameters, request, OriginCreate.fluxKontextImageEdit.getValue());

            // 异步轮询任务状态
            startPollingTask(requestId, user.getLoginName());

            return result;

        } catch (Exception e) {
            log.error("Flux Kontext image-edit failed", e);
            if (e instanceof LogicException) {
                throw e;
            }
            throw new LogicException(LogicErrorCode.THIRD_PARTY_API_ERROR);
        }
    }

    /**
     * 保存任务信息到Redis
     */
    private void saveTaskToRedis(String requestId, String loginName, String markId) {
        try {
            // 保存任务基本信息
            String taskKey = FLUX_TASK_PREFIX + requestId;
            redisService.setDataToHash(taskKey, "loginName", loginName);
            redisService.setDataToHash(taskKey, "markId", markId);
            redisService.setDataToHash(taskKey, "createTime", String.valueOf(System.currentTimeMillis()));
            redisService.expire(taskKey, 3600); // 1小时过期

            // 添加到用户任务列表
            String userTaskKey = FLUX_USER_TASK_PREFIX + loginName;
            redisService.setDataToHash(userTaskKey, requestId, markId);
            redisService.expire(userTaskKey, 3600);

        } catch (Exception e) {
            log.error("Failed to save task to redis: " + requestId, e);
        }
    }

    /**
     * 保存PromptRecord到数据库
     */
    private void savePromptRecord(String requestId, String prompt, User user, String aspectRatio,
                                  Integer seed, String markId, Boolean fastHour, String platform,
                                  GenGenericPara genParameters, Object request, String originCreate) {
        try {
            PromptRecord promptRecord = new PromptRecord();
            promptRecord.setPromptId(requestId);
            promptRecord.setLoginName(user.getLoginName());
            promptRecord.setPrompt(prompt);
            promptRecord.setNegativePrompt("");
            promptRecord.setCreateBy(user.getLoginName());
            promptRecord.setCreateTime(LocalDateTime.now());
            promptRecord.setGenStartTime(LocalDateTime.now());
            promptRecord.setOriginCreate(originCreate);
            promptRecord.setBatchSize(1);
            promptRecord.setMarkId(markId);
            promptRecord.setAspectRatio(aspectRatio);
            promptRecord.setFastHour(fastHour);
            promptRecord.setPlatform(platform);
            promptRecord.setPromptParams(JsonUtils.writeToJsonNode(request));

            if (genParameters != null) {
                promptRecord.setGenInfo(JsonUtils.writeToJsonNode(genParameters));
                promptRecord.setModelId(genParameters.getModel_id());
            }

            promptRecordMapper.insert(promptRecord);
            log.info("Saved PromptRecord for requestId: {}", requestId);

        } catch (Exception e) {
            log.error("Failed to save PromptRecord: " + requestId, e);
        }
    }

    /**
     * 异步开始轮询任务状态
     */
    @Async
    public CompletableFuture<Void> startPollingTask(String requestId, String loginName) {
        return CompletableFuture.runAsync(() -> {
            pollTaskStatus(requestId, loginName);
        });
    }

    /**
     * 轮询任务状态
     */
    private void pollTaskStatus(String requestId, String loginName) {
        int attempts = 0;

        while (attempts < fluxKontextConfig.getMaxPollingAttempts()) {
            try {
                // 检查Redis中任务是否还存在（可能已被手动取消）
                String taskKey = FLUX_TASK_PREFIX + requestId;
                if (!redisService.hasKey(taskKey)) {
                    log.info("Task {} was cancelled or cleaned up", requestId);
                    break;
                }

                // 调用API获取结果
                Call<FluxKontextResponse.TaskResultResponse> call = fluxKontextApiClient.getResult(
                        fluxKontextConfig.getApiKey(), requestId);
                Response<FluxKontextResponse.TaskResultResponse> response = call.execute();

                if (response.isSuccessful() && response.body() != null) {
                    FluxKontextResponse.TaskResultResponse result = response.body();
                    FluxKontextTaskStatus status = FluxKontextTaskStatus.fromStatus(result.getStatus());

                    log.info("Polling task {}, status: {}, attempt: {}", requestId, status.getStatus(), attempts + 1);

                    if (status.isFinalStatus()) {
                        // 处理最终结果
                        fluxKontextCallbackService.handleTaskResult(result, loginName);
                        break;
                    }
                } else {
                    log.warn("Failed to poll task status for {}: {}", requestId,
                            response.errorBody() != null ? response.errorBody().string() : "Unknown error");
                }

                // 等待下次轮询
                Thread.sleep(fluxKontextConfig.getPollingInterval());
                attempts++;

            } catch (Exception e) {
                log.error("Error polling task status for " + requestId, e);
                attempts++;

                if (attempts >= fluxKontextConfig.getMaxPollingAttempts()) {
                    // 达到最大重试次数，标记为失败
                    handlePollingTimeout(requestId, loginName);
                }
            }
        }
    }

    /**
     * 处理轮询超时
     */
    private void handlePollingTimeout(String requestId, String loginName) {
        try {
            log.warn("Polling timeout for task: {}", requestId);

            // 更新数据库记录为失败
            LambdaUpdateWrapper<PromptRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PromptRecord::getPromptId, requestId)
                    .eq(PromptRecord::getLoginName, loginName)
                    .set(PromptRecord::getDel, true)
                    .set(PromptRecord::getFailureMessage, "Task polling timeout")
                    .set(PromptRecord::getGenEndTime, LocalDateTime.now())
                    .set(PromptRecord::getUpdateTime, LocalDateTime.now());

            promptRecordMapper.update(null, updateWrapper);

            // 清理Redis
            cleanupTaskFromRedis(requestId, loginName);

        } catch (Exception e) {
            log.error("Error handling polling timeout for " + requestId, e);
        }
    }

    /**
     * 清理Redis任务信息
     */
    private void cleanupTaskFromRedis(String requestId, String loginName) {
        try {
            // 删除任务信息
            String taskKey = FLUX_TASK_PREFIX + requestId;
            redisService.delete(taskKey);

            // 从用户任务列表中删除
            String userTaskKey = FLUX_USER_TASK_PREFIX + loginName;
            redisService.deleteFieldFromHash(userTaskKey, requestId);

        } catch (Exception e) {
            log.error("Error cleaning up task from redis for requestId: " + requestId, e);
        }
    }

    /**
     * 检查用户并发任务数限制
     */
    public boolean checkFluxKontextConcurrentJobs(User user, String markId) {
        try {
            String userTaskKey = FLUX_USER_TASK_PREFIX + user.getLoginName();
            int currentJobs = redisService.getHashSize(userTaskKey);

            // 最大并发任务数限制（可配置）
            int maxConcurrentJobs = 10;

            if (currentJobs >= maxConcurrentJobs) {
                log.warn("User {} exceeded max concurrent jobs limit: {}/{}",
                        user.getLoginName(), currentJobs, maxConcurrentJobs);
                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("Error checking concurrent jobs for user: " + user.getLoginName(), e);
            return false;
        }
    }

    /**
     * 获取任务状态
     */
    public FluxKontextResponse.TaskResultResponse getTaskStatus(String requestId) {
        try {
            Call<FluxKontextResponse.TaskResultResponse> call = fluxKontextApiClient.getResult(
                    fluxKontextConfig.getApiKey(), requestId);
            Response<FluxKontextResponse.TaskResultResponse> response = call.execute();

            if (response.isSuccessful() && response.body() != null) {
                return response.body();
            } else {
                log.error("Failed to get task status for {}: {}", requestId,
                        response.errorBody() != null ? response.errorBody().string() : "Unknown error");
                return null;
            }
        } catch (Exception e) {
            log.error("Error getting task status for " + requestId, e);
            return null;
        }
    }
}