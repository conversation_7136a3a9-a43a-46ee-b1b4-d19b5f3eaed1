package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lx.pl.client.MidjourneyApiClient;
import com.lx.pl.config.MidjourneyConfig;
import com.lx.pl.constant.LockPrefixConstant;
import com.lx.pl.db.mysql.gen.entity.PromptFile;
import com.lx.pl.db.mysql.gen.entity.PromptRecord;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.PromptFileMapper;
import com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper;
import com.lx.pl.db.mysql.gen.mapper.UserMapper;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.midjourney.MidjourneyRequest;
import com.lx.pl.dto.midjourney.MidjourneyResponse;
import com.lx.pl.dto.mq.MjImageProcessVo;
import com.lx.pl.dto.mq.MjTaskPollingVo;
import com.lx.pl.enums.AlarmEnum;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.enums.MidjourneyTaskStatus;
import com.lx.pl.enums.OriginCreate;
import com.lx.pl.exception.LogicException;
import com.lx.pl.mq.message.CommonMqMessage;
import com.lx.pl.mq.message.RMqMessage;
import com.lx.pl.mq.producer.NormalMessageProducer;
import com.lx.pl.service.message.DingTalkAlert;
import com.lx.pl.util.AspectRatioUtils;
import com.lx.pl.util.JsonUtils;
import com.lx.pl.util.LogicUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.lx.pl.service.GenService.USER_TODAY_CREATE_IMG_NUMS;
import static com.lx.pl.service.LoadBalanceService.USER_TASK_TIMESTAMP;

/**
 * Midjourney服务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MidjourneyService {

    @Autowired
    private MidjourneyApiClient midjourneyApiClient;

    @Autowired
    private MidjourneyConfig midjourneyConfig;

    @Autowired
    private RedisService redisService;

    @Autowired
    private PromptRecordMapper promptRecordMapper;

    @Autowired
    private PromptFileMapper promptFileMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private VipService vipService;

    @Autowired
    private RedissonClient redissonClient;

    @Value("${ttapimj.modelId}")
    String ttapiMjModelId;

    @Autowired
    private NormalMessageProducer normalMessageProducer;

    @Value("${rocketmq.midjourney.polling.topic:tp_midjourney_polling_test}")
    private String mjPollingTopic;

    @Value("${rocketmq.midjourney.polling.tag:tag_midjourney_polling_test}")
    private String mjPollingTag;

    @Value("${rocketmq.image.process.tag}")
    private String mjImageProcessTag;
    @Value("${rocketmq.piclumen.topic}")
    private String mjImageProcessTopic;

    private static final String TTAPI_MJ_TASK_LOCK = "ttapi_mj_task_";
    private static final String TTAPI_MJ_TASK_PREFIX = "ttapi:mj:task:";
    private static final String TTAPI_MJ_USER_CONCURRENT_PREFIX = "ttapi:mj:concurrent";
    private static final String TTAPI_MJ_IMG_PREFIX = "ttapi:mj:do_img:";

    @Autowired
    private DingTalkAlert dingTalkAlert;
    @Autowired
    private LumenService lumenService;

    /**
     * 生成图像 - /imagine
     *
     * @param prompt 提示词
     * @param mode   模式
     * @param user   用户
     */
    public MidjourneyResponse.BaseResponseData imagine(String prompt, String mode, User user) {
        return imagine(prompt, mode, user, null, null, null, null);
    }

    /**
     * 生成图像 - /imagine (完整版本，支持PromptRecord入库)
     *
     * @param prompt        提示词
     * @param mode          模式
     * @param user          用户
     * @param markId        标记ID
     * @param fastHour      是否快速生图
     * @param platform      平台
     * @param genParameters 原始生图参数（用于记录）
     */
    public MidjourneyResponse.BaseResponseData imagine(String prompt, String mode, User user,
                                                       String markId, Boolean fastHour, String platform, GenGenericPara genParameters) {
        try {
            // 构建请求
            MidjourneyRequest.ImagineRequest request = new MidjourneyRequest.ImagineRequest();
            request.setPrompt(prompt);
            request.setMode(StringUtil.isNotBlank(mode) ? mode : midjourneyConfig.getDefaultMode());
            request.setHookUrl(midjourneyConfig.getCallbackUrl());
            request.setTimeout(midjourneyConfig.getDefaultTimeout());
            request.setTranslation(midjourneyConfig.getTranslationEnabled());
            request.setGetUImages(Boolean.TRUE);

            log.info("Midjourney imagine request: {}", JsonUtils.writeToString(request));

            // 调用API
            Call<MidjourneyResponse> call = midjourneyApiClient.imagine(midjourneyConfig.getApiKey(), request);
            Response<MidjourneyResponse> response = call.execute();

            if (!response.isSuccessful()) {
                log.error("Midjourney imagine API call failed: {}", response.errorBody().string());
                throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
            }

            MidjourneyResponse midjourneyResponse = response.body();
            if (!"SUCCESS".equals(midjourneyResponse.getStatus())) {
                log.error("Midjourney imagine failed: {}", midjourneyResponse.getMessage());
                throw new LogicException(LogicErrorCode.MIDJOURNEY_GENERATION_FAILED);
            }

            // 解析响应数据
            MidjourneyResponse.BaseResponseData responseData = JsonUtils.convertValue(
                    midjourneyResponse.getData(), MidjourneyResponse.BaseResponseData.class);

            // 添加到并发任务列表
            String userConcurrentKey = TTAPI_MJ_USER_CONCURRENT_PREFIX;
            redisService.putDataToHash(userConcurrentKey, responseData.getJobId(), System.currentTimeMillis());


            // 如果提供了markId等参数，则保存PromptRecord到数据库
            if (StringUtil.isNotBlank(markId)) {
                savePromptRecord(responseData.getJobId(), prompt, mode, user, markId, fastHour, platform, genParameters);

                // 保存MJ任务状态到Redis（模拟传统任务的方式）
                saveMjTaskStatusToRedis(responseData.getJobId(), markId, user.getLoginName());

                // 启动任务状态轮询
                startTaskStatusPolling(responseData.getJobId(), user.getLoginName(), midjourneyConfig.getFirstDelaySeconds());
            }

            log.info("Midjourney imagine success, jobId: {}", responseData.getJobId());
            return responseData;

        } catch (IOException e) {
            log.error("Midjourney imagine error", e);
            throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
        } catch (LogicException e) {
            // 重新抛出业务异常，保留原始错误信息
            throw e;
        } catch (Exception e) {
            log.error("Midjourney imagine error", e);
            throw new LogicException(LogicErrorCode.MIDJOURNEY_GENERATION_FAILED);
        }
    }

//    /**
//     * 执行操作 - U1~U4、V1~V4等
//     */
//    public MidjourneyResponse.BaseResponseData action(String jobId, String actionType, User user) {
//        try {
//            // 验证操作类型
//            MidjourneyActionType.fromAction(actionType);
//
//            // 构建请求
//            MidjourneyRequest.ActionRequest request = new MidjourneyRequest.ActionRequest();
//            request.setJobId(jobId);
//            request.setAction(actionType);
//            request.setHookUrl(midjourneyConfig.getCallbackUrl());
//            request.setTimeout(midjourneyConfig.getDefaultTimeout());
//
//            log.info("Midjourney action request: {}", JsonUtils.writeToString(request));
//
//            // 调用API
//            Call<MidjourneyResponse> call = midjourneyApiClient.action(midjourneyConfig.getApiKey(), request);
//            Response<MidjourneyResponse> response = call.execute();
//
//            if (!response.isSuccessful()) {
//                log.error("Midjourney action API call failed: {}", response.errorBody().string());
//                throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
//            }
//
//            MidjourneyResponse midjourneyResponse = response.body();
//            if (!"SUCCESS".equals(midjourneyResponse.getStatus())) {
//                log.error("Midjourney action failed: {}", midjourneyResponse.getMessage());
//                throw new LogicException(LogicErrorCode.MIDJOURNEY_ACTION_FAILED);
//            }
//
//            // 解析响应数据
//            MidjourneyResponse.BaseResponseData responseData = JsonUtils.convertValue(
//                    midjourneyResponse.getData(), MidjourneyResponse.BaseResponseData.class);
//
//            // 保存任务信息到Redis
////            saveTaskToRedis(responseData.getJobId(), user.getLoginName(), actionType, jobId);
//
//            // 添加到并发任务列表
//            String userConcurrentKey = MIDJOURNEY_USER_CONCURRENT_PREFIX + user.getLoginName();
//            redisService.putDataToHash(userConcurrentKey, responseData.getJobId(), System.currentTimeMillis(), 2, TimeUnit.HOURS);
//
//            log.info("Midjourney action success, jobId: {}", responseData.getJobId());
//            return responseData;
//
//        } catch (IOException e) {
//            log.error("Midjourney action error", e);
//            throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
//        } catch (Exception e) {
//            log.error("Midjourney action error", e);
//            throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
//        }
//    }
//
//    /**
//     * 图像合成 - /blend
//     */
//    public MidjourneyResponse.BaseResponseData blend(List<String> imageBase64List, String dimensions, String mode, User user) {
//        try {
//            if (imageBase64List == null || imageBase64List.size() < 2 || imageBase64List.size() > 5) {
//                throw new LogicException(LogicErrorCode.ILLEGAL_REQUEST);
//            }
//
//            // 构建请求
//            MidjourneyRequest.BlendRequest request = new MidjourneyRequest.BlendRequest();
//            request.setImgBase64Array(imageBase64List);
//            request.setDimensions(StringUtil.isNotBlank(dimensions) ? dimensions : "SQUARE");
//            request.setMode(StringUtil.isNotBlank(mode) ? mode : midjourneyConfig.getDefaultMode());
//            request.setHookUrl(midjourneyConfig.getCallbackUrl());
//            request.setTimeout(midjourneyConfig.getDefaultTimeout());
//
//            log.info("Midjourney blend request: {}", JsonUtils.writeToString(request));
//
//            // 调用API
//            Call<MidjourneyResponse> call = midjourneyApiClient.blend(midjourneyConfig.getApiKey(), request);
//            Response<MidjourneyResponse> response = call.execute();
//
//            if (!response.isSuccessful()) {
//                log.error("Midjourney blend API call failed: {}", response.errorBody().string());
//                throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
//            }
//
//            MidjourneyResponse midjourneyResponse = response.body();
//            if (!"SUCCESS".equals(midjourneyResponse.getStatus())) {
//                log.error("Midjourney blend failed: {}", midjourneyResponse.getMessage());
//                throw new LogicException(LogicErrorCode.MIDJOURNEY_ACTION_FAILED);
//            }
//
//            // 解析响应数据
//            MidjourneyResponse.BaseResponseData responseData = JsonUtils.convertValue(
//                    midjourneyResponse.getData(), MidjourneyResponse.BaseResponseData.class);
//
//            // 保存任务信息到Redis
////            saveTaskToRedis(responseData.getJobId(), user.getLoginName(), "blend", "blend_images");
//
//            log.info("Midjourney blend success, jobId: {}", responseData.getJobId());
//            return responseData;
//
//        } catch (IOException e) {
//            log.error("Midjourney blend error", e);
//            throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
//        } catch (Exception e) {
//            log.error("Midjourney blend error", e);
//            throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
//        }
//    }
//
//    /**
//     * 图像描述 - /describe
//     */
//    public MidjourneyResponse.BaseResponseData describe(String imageBase64, String imageUrl, String mode, User user) {
//        try {
//            if (StringUtil.isBlank(imageBase64) && StringUtil.isBlank(imageUrl)) {
//                throw new LogicException(LogicErrorCode.ILLEGAL_REQUEST);
//            }
//
//            // 构建请求
//            MidjourneyRequest.DescribeRequest request = new MidjourneyRequest.DescribeRequest();
//            request.setBase64(imageBase64);
//            request.setUrl(imageUrl);
//            request.setMode(StringUtil.isNotBlank(mode) ? mode : midjourneyConfig.getDefaultMode());
//            request.setHookUrl(midjourneyConfig.getCallbackUrl());
//            request.setTimeout(midjourneyConfig.getDefaultTimeout());
//
//            log.info("Midjourney describe request: {}", JsonUtils.writeToString(request));
//
//            // 调用API
//            Call<MidjourneyResponse> call = midjourneyApiClient.describe(midjourneyConfig.getApiKey(), request);
//            Response<MidjourneyResponse> response = call.execute();
//
//            if (!response.isSuccessful()) {
//                log.error("Midjourney describe API call failed: {}", response.errorBody().string());
//                throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
//            }
//
//            MidjourneyResponse midjourneyResponse = response.body();
//            if (!"SUCCESS".equals(midjourneyResponse.getStatus())) {
//                log.error("Midjourney describe failed: {}", midjourneyResponse.getMessage());
//                throw new LogicException(LogicErrorCode.MIDJOURNEY_ACTION_FAILED);
//            }
//
//            // 解析响应数据
//            MidjourneyResponse.BaseResponseData responseData = JsonUtils.convertValue(
//                    midjourneyResponse.getData(), MidjourneyResponse.BaseResponseData.class);
//
//            // 保存任务信息到Redis
////            saveTaskToRedis(responseData.getJobId(), user.getLoginName(), "describe", "describe_image");
//
//            log.info("Midjourney describe success, jobId: {}", responseData.getJobId());
//            return responseData;
//
//        } catch (IOException e) {
//            log.error("Midjourney describe error", e);
//            throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
//        } catch (Exception e) {
//            log.error("Midjourney describe error", e);
//            throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
//        }
//    }

    /**
     * 查询任务状态
     */
    public MidjourneyResponse.TaskStatusResponse getTaskStatus(String jobId) {
        try {
            // 构建请求
            MidjourneyRequest.FetchRequest request = new MidjourneyRequest.FetchRequest();
            request.setJobId(jobId);

            // 调用API
            Call<MidjourneyResponse.TaskStatusResponse> call = midjourneyApiClient.fetch(midjourneyConfig.getApiKey(), request);
            Response<MidjourneyResponse.TaskStatusResponse> response = call.execute();

            if (!response.isSuccessful()) {
                log.error("Midjourney fetch API call failed: {}", response.errorBody().string());
                throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
            }

            MidjourneyResponse.TaskStatusResponse taskStatus = response.body();
            log.info("Midjourney task status: {}", JsonUtils.writeToString(taskStatus));

            return taskStatus;

        } catch (IOException e) {
            log.error("Midjourney fetch error", e);
            throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
        } catch (Exception e) {
            log.error("Midjourney fetch error", e);
            throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
        }
    }

    /**
     * Prompt效验 - 检查prompt是否包含被禁止的词汇
     */
    public MidjourneyResponse.PromptCheckResponse promptCheck(String prompt) {
        try {
            // 构建请求
            MidjourneyRequest.PromptCheckRequest request = new MidjourneyRequest.PromptCheckRequest();
            request.setPrompt(prompt);

            log.info("Midjourney prompt check request: {}", JsonUtils.writeToString(request));

            // 调用API
            Call<MidjourneyResponse.PromptCheckResponse> call = midjourneyApiClient.promptCheck(midjourneyConfig.getApiKey(), request);
            Response<MidjourneyResponse.PromptCheckResponse> response = call.execute();

            if (!response.isSuccessful()) {
                String errorBody = "Unknown error";
                //获取errorBody中的message，可以转为promptCheckResponse对象
                MidjourneyResponse.PromptCheckResponse promptCheckResponse = JsonUtils.fromString(response.errorBody().string(), MidjourneyResponse.PromptCheckResponse.class);
                if (promptCheckResponse != null) {
                    errorBody = promptCheckResponse.getMessage();
                }
                log.error("Midjourney prompt check API call failed with status {}: {}", response.code(), errorBody);
                throw new LogicException(LogicErrorCode.MIDJOURNEY_PROMPT_CHECK_FAILED, errorBody);
            }

            MidjourneyResponse.PromptCheckResponse promptCheckResponse = response.body();
            log.info("Midjourney prompt check result: {}", JsonUtils.writeToString(promptCheckResponse));
            return promptCheckResponse;

        } catch (LogicException e) {
            // 重新抛出业务异常，保留原始错误信息
            throw e;
        } catch (Exception e) {
            log.error("Midjourney prompt check error", e);
            throw new LogicException(LogicErrorCode.MIDJOURNEY_PROMPT_CHECK_FAILED);
        }
    }

    /**
     * 保存PromptRecord到数据库
     */
    private void savePromptRecord(String jobId, String prompt, String mode, User user, String markId,
                                  Boolean fastHour, String platform, GenGenericPara genParameters) {
        try {
            PromptRecord promptRecord = new PromptRecord();
            promptRecord.setLoginName(user.getLoginName());
            promptRecord.setPromptId(jobId);
            promptRecord.setPrompt(prompt);
            promptRecord.setNegativePrompt(""); // Midjourney的负面提示词通过--no参数处理
            promptRecord.setCreateBy(user.getLoginName());
            promptRecord.setCreateTime(LocalDateTime.now());
            promptRecord.setGenStartTime(LocalDateTime.now());
            promptRecord.setOriginCreate(OriginCreate.create.getValue());
            promptRecord.setBatchSize(4); // Midjourney每次生成4张图
            promptRecord.setModelId(ttapiMjModelId);
            promptRecord.setMarkId(markId);
            promptRecord.setGenMode(mode);
            promptRecord.setFastHour(fastHour != null ? fastHour : Boolean.FALSE);
            promptRecord.setPlatform(platform);

            // 如果有原始参数，保存为genInfo
            if (genParameters != null) {
                promptRecord.setGenInfo(JsonUtils.writeToJsonNode(genParameters));
            }

            // 设置宽高比，根据前端SHAPE_ALL配置处理
            String aspectRatio = "1024 x 1024"; // 默认值
            if (genParameters instanceof GenGenericPara) {
                GenGenericPara genPara = (GenGenericPara) genParameters;
                aspectRatio = AspectRatioUtils.getAspectRatio(genPara);
            }
            promptRecord.setAspectRatio(aspectRatio);

            promptRecordMapper.insert(promptRecord);
            log.info("Saved PromptRecord for Midjourney task, jobId: {}, markId: {}", jobId, markId);

        } catch (Exception e) {
            log.error("Save PromptRecord error for jobId: {}", jobId, e);
        }
    }

    /**
     * 保存MJ任务状态到Redis（模拟传统任务的方式）
     */
    private void saveMjTaskStatusToRedis(String jobId, String markId, String loginName) {
        try {
            // 1. 保存jobId -> markId的映射（用于任务类型识别）
            redisService.stringSet(jobId, markId, 2, TimeUnit.HOURS);

            // 2. 保存markId -> loginName的映射（模拟传统任务）
            redisService.stringSet(markId, loginName, 2, TimeUnit.HOURS);

            // 3. 在用户hash中设置任务状态为-1（新建状态）
            redisService.putDataToHash(loginName, markId, -1, 2, TimeUnit.HOURS);

            // 4. 设置任务时间戳
            redisService.set(USER_TASK_TIMESTAMP + markId, System.currentTimeMillis(), 2, TimeUnit.HOURS);

            String taskKey = TTAPI_MJ_TASK_PREFIX + jobId;
            redisService.stringSet(taskKey, markId, 10, TimeUnit.MINUTES);

            // 预扣
            lumenService.notFinishTask(loginName);
            log.info("Saved MJ task status to Redis - jobId: {}, markId: {}, loginName: {}", jobId, markId, loginName);

        } catch (Exception e) {
            log.error("Save MJ task status to Redis error", e);
        }
    }

    /**
     * 更新MJ任务在Redis中的状态
     */
    private void updateMjTaskStatusInRedis(String jobId, Integer index) {
        try {
            // 获取markId
            String markId = redisService.stringGet(jobId);
            if (StringUtil.isBlank(markId)) {
                log.warn("Cannot find markId for jobId: {}", jobId);
                return;
            }

            // 获取loginName
            String loginName = redisService.stringGet(markId);
            if (StringUtil.isBlank(loginName)) {
                log.warn("Cannot find loginName for markId: {}", markId);
                return;
            }

            // 更新用户hash中的任务状态
            redisService.putDataToHash(loginName, markId, index);

            log.debug("Updated MJ task status in Redis - jobId: {}, markId: {}, index: {}", jobId, markId, index);

        } catch (Exception e) {
            log.error("Update MJ task status in Redis error", e);
        }
    }

    /**
     * 检查用户Midjourney并发任务数是否超过限制
     *
     * @param user  用户信息
     * @param jobId 任务ID（用于预占位）
     * @return true表示超过限制，false表示可以继续
     */
    public boolean checkMidjourneyConcurrentJobs(User user, String jobId) {
        String lockKey = LockPrefixConstant.CONCURRENT_EXECUTION_LOCK_PREFIX + "ttapi:" + user.getId();
        RLock lock = redissonClient.getLock(lockKey);

        try {
            lock.lock();

            // 获取用户当前的Midjourney任务列表
            String userConcurrentKey = TTAPI_MJ_USER_CONCURRENT_PREFIX;
            List<String> userTaskList = redisService.getAllKeysFromHash(userConcurrentKey);

            // 清理已过期的任务
            cleanExpiredMidjourneyTasks(userConcurrentKey, userTaskList);

            // 重新获取清理后的任务列表
            userTaskList = redisService.getAllKeysFromHash(userConcurrentKey);

            // 检查是否超过最大并发数限制（默认10个）
            int maxConcurrentJobs = midjourneyConfig.getMaxConcurrentJobs() != null ?
                    midjourneyConfig.getMaxConcurrentJobs() : 10;

            if (userTaskList.size() >= maxConcurrentJobs) {
                log.warn("User {} Midjourney concurrent jobs limit exceeded: {}/{}",
                        user.getLoginName(), userTaskList.size(), maxConcurrentJobs);
                return true; // 超过限制
            }

            log.info("User {} Midjourney concurrent jobs check passed: {}/{}",
                    user.getLoginName(), userTaskList.size() + 1, maxConcurrentJobs);
            return false; // 未超过限制

        } catch (Exception e) {
            log.error("Check Midjourney concurrent jobs error for user: {}", user.getLoginName(), e);
            return true; // 出错时保守处理，拒绝请求
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 清理已过期的Midjourney任务
     */
    private void cleanExpiredMidjourneyTasks(String userConcurrentKey, List<String> taskList) {
        if (taskList == null || taskList.isEmpty()) {
            return;
        }

        try {
            for (String jobId : taskList) {
                String taskKey = TTAPI_MJ_TASK_PREFIX + jobId;
                if (!redisService.hasKey(taskKey)) {
                    // 任务已过期，从并发列表中移除
                    redisService.deleteFieldFromHash(userConcurrentKey, jobId);
                    log.debug("Removed expired Midjourney task: {}", jobId);
                }
            }
        } catch (Exception e) {
            log.error("Clean expired Midjourney tasks error", e);
        }
    }

    /**
     * 移除用户的Midjourney并发任务（任务完成时调用）
     */
    public void removeMidjourneyConcurrentJob(String loginName, String jobId) {
        try {
            String userConcurrentKey = TTAPI_MJ_USER_CONCURRENT_PREFIX + loginName;
            redisService.deleteFieldFromHash(userConcurrentKey, jobId);
            log.debug("Removed Midjourney concurrent job: {} for user: {}", jobId, loginName);
        } catch (Exception e) {
            log.error("Remove Midjourney concurrent job error", e);
        }
    }

    /**
     * 启动任务状态轮询（使用RocketMQ延时队列）
     * 每2秒查询一次任务状态，直到任务完成或失败
     */
    public void startTaskStatusPolling(String jobId, String loginName, long delaySeconds) {
        log.info("Starting task status polling for jobId: {}, user: {}", jobId, loginName);

        updateMjTaskStatusInRedis(jobId, 0);

        int maxAttempts = midjourneyConfig.getMaxPollingAttempts() != null ?
                midjourneyConfig.getMaxPollingAttempts() : 30;
        int pollingInterval = midjourneyConfig.getPollingIntervalSeconds() != null ?
                midjourneyConfig.getPollingIntervalSeconds() * 1000 : 2000; // 转换为毫秒

        // 创建轮询消息
        MjTaskPollingVo pollingVo = new MjTaskPollingVo(
                jobId,
                loginName,
                0, // 初始轮询次数
                maxAttempts,
                pollingInterval,
                System.currentTimeMillis()
        );

        // 发送延时消息，延时pollingInterval毫秒后开始第一次轮询
        sendPollingMessage(pollingVo, delaySeconds);
    }

    /**
     * 发送轮询延时消息
     */
    private void sendPollingMessage(MjTaskPollingVo pollingVo, long delaySeconds) {
        try {
            CommonMqMessage<MjTaskPollingVo> mqMessage = new RMqMessage<>(
                    mjPollingTopic,
                    mjPollingTag,
                    pollingVo.getJobId() + "_" + pollingVo.getCurrentAttempt()
            );
            mqMessage.setMessage(pollingVo);

            normalMessageProducer.syncDelaySend(mqMessage, delaySeconds);

            log.debug("Sent MJ polling message for jobId: {}, attempt: {}, delay: {}s",
                    pollingVo.getJobId(), pollingVo.getCurrentAttempt(), delaySeconds);
        } catch (Exception e) {
            log.error("Failed to send MJ polling message for jobId: {}", pollingVo.getJobId(), e);
        }
    }

    /**
     * 处理任务状态轮询（从MQ消费者调用）
     */
    public void handleTaskStatusPolling(MjTaskPollingVo pollingVo) {
        String jobId = pollingVo.getJobId();
        String loginName = pollingVo.getLoginName();

        try {
            // 检查任务是否还存在于Redis中（可能已被手动取消或回调处理）
            String taskKey = TTAPI_MJ_TASK_PREFIX + jobId;
            if (!redisService.hasKey(taskKey) && !redisService.hasKey(jobId)) {
                log.info("Task {} was cancelled or already processed by callback", jobId);
                return;
            }

            // 查询任务状态
            MidjourneyResponse.TaskStatusResponse taskStatus = getTaskStatus(jobId);

            if (taskStatus == null) {
                log.warn("Failed to get task status for jobId: {}, attempt: {}", jobId, pollingVo.getCurrentAttempt());
                scheduleNextPolling(pollingVo);
                return;
            }

            String status = taskStatus.getStatus();
            log.debug("Task status polling - jobId: {}, status: {}, attempt: {}", jobId, status, pollingVo.getCurrentAttempt());

            MidjourneyTaskStatus taskStatusEnum = MidjourneyTaskStatus.fromStatus(status);

            // 根据状态处理
            switch (taskStatusEnum) {
                case SUCCESS:
                    log.info("Task completed successfully - jobId: {}", jobId);
                    handleTaskSuccess(jobId, loginName, taskStatus);
                    return; // 任务完成，不再轮询

                case FAILED:
                    log.warn("Task failed - jobId: {}", jobId);
                    handleTaskFailure(jobId, loginName);
                    return; // 任务失败，不再轮询

                case PENDING_QUEUE:
                    // 更新Redis状态为排队中
                    updateMjTaskStatusInRedis(jobId, 0);
                    log.debug("Task in queue - jobId: {}, status: {}", jobId, status);
                    scheduleNextPolling(pollingVo);
                    break;

                case ON_QUEUE:
                    // 更新Redis状态为执行中
                    updateMjTaskStatusInRedis(jobId, 0);
                    log.debug("Task running - jobId: {}, status: {}", jobId, status);
                    scheduleNextPolling(pollingVo);
                    break;

                default:
                    log.warn("Unknown task status: {} for jobId: {}", status, jobId);
                    scheduleNextPolling(pollingVo);
                    break;
            }

        } catch (Exception e) {
            log.error("Error during task status polling for jobId: {}, attempt: {}", jobId, pollingVo.getCurrentAttempt(), e);
            scheduleNextPolling(pollingVo);
        }
    }

    /**
     * 安排下一次轮询
     */
    private void scheduleNextPolling(MjTaskPollingVo pollingVo) {
        int nextAttempt = pollingVo.getCurrentAttempt() + 1;

        if (nextAttempt >= pollingVo.getMaxAttempts()) {
            // 超时处理
            log.warn("Task status polling timeout for jobId: {} after {} attempts", pollingVo.getJobId(), pollingVo.getMaxAttempts());
            handleTaskTimeout(pollingVo.getJobId(), pollingVo.getLoginName());
            return;
        }

        // 创建下一次轮询消息
        MjTaskPollingVo nextPollingVo = new MjTaskPollingVo(
                pollingVo.getJobId(),
                pollingVo.getLoginName(),
                nextAttempt,
                pollingVo.getMaxAttempts(),
                pollingVo.getPollingInterval(),
                pollingVo.getCreateTimestamp()
        );

        // 发送延时消息
        sendPollingMessage(nextPollingVo, pollingVo.getPollingInterval());
    }

    /**
     * 处理任务成功
     */
    public void handleTaskSuccess(String jobId, String loginName, MidjourneyResponse.TaskStatusResponse taskStatus) {
        //分布式锁
        String lockKey = TTAPI_MJ_TASK_LOCK + jobId;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            lock.lock();
            //图片处理锁-防止重复处理
            String taskKey = TTAPI_MJ_IMG_PREFIX + jobId;
            String s = redisService.stringGet(taskKey);
            if (StringUtil.isNotBlank(s)) {
                return;
            }

            redisService.stringSet(taskKey, jobId, 5, TimeUnit.MINUTES);

            if (promptRecordFinished(jobId, loginName)) {
                return;
            }

            MidjourneyResponse.TaskData taskData = taskStatus.getData();
            if (taskData == null) {
                log.warn("Task data is null for successful jobId: {}", jobId);
                return;
            }

            // 发送图片处理MQ消息（取消原来的processTaskImages调用，改为在MQ消息处理结果中直接入库）
            if (!CollectionUtils.isEmpty(taskData.getImages())) {
                sendImageProcessMessage(jobId, loginName, taskData);
            }

            log.info("Successfully processed task completion for jobId: {}", jobId);

        } catch (Exception e) {
            log.error("Error handling task success for jobId: " + jobId, e);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    /**
     * 处理任务失败
     */
    public void handleTaskFailure(String jobId, String loginName) {
        //分布式锁
        String lockKey = TTAPI_MJ_TASK_LOCK + jobId;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            lock.lock();
            // 更新任务状态为失败
            LambdaUpdateWrapper<PromptRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PromptRecord::getPromptId, jobId)
                    .eq(PromptRecord::getLoginName, loginName)
                    .set(PromptRecord::getDel, true)
                    .set(PromptRecord::getFailureMessage, "Task failed")
                    .set(PromptRecord::getUpdateTime, LocalDateTime.now());

            promptRecordMapper.update(null, updateWrapper);

            // 清理Redis任务信息
            cleanupTaskFromRedis(jobId, loginName);

            log.info("Processed failed task for jobId: {}", jobId);

        } catch (Exception e) {
            log.error("Error handling task failure for jobId: " + jobId, e);
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    /**
     * 处理任务超时
     */
    public void handleTaskTimeout(String jobId, String loginName) {
        try {
            // 将超时任务标记为失败
            LambdaUpdateWrapper<PromptRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PromptRecord::getPromptId, jobId)
                    .eq(PromptRecord::getLoginName, loginName)
                    .set(PromptRecord::getDel, true)
                    .set(PromptRecord::getUpdateTime, LocalDateTime.now());

            promptRecordMapper.update(null, updateWrapper);

            // 清理Redis任务信息
            cleanupTaskFromRedis(jobId, loginName);

            log.warn("Processed timeout task for jobId: {}", jobId);

        } catch (Exception e) {
            log.error("Error handling task timeout for jobId: " + jobId, e);
        }
    }

    /**
     * 更新PromptRecord的结束时间
     */
    public void updatePromptRecordEndTime(String jobId, String loginName) {
        LambdaUpdateWrapper<PromptRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PromptRecord::getPromptId, jobId)
                .eq(PromptRecord::getLoginName, loginName)
                .set(PromptRecord::getGenEndTime, LocalDateTime.now())
                .set(PromptRecord::getUpdateTime, LocalDateTime.now());

        promptRecordMapper.update(null, updateWrapper);
    }

    /**
     * 查询对应的任务状态
     */
    public Boolean promptRecordFinished(String jobId, String loginName) {
        LambdaQueryWrapper<PromptRecord> qw = new LambdaQueryWrapper<>();
        qw.eq(PromptRecord::getPromptId, jobId)
                .eq(PromptRecord::getLoginName, loginName);
        PromptRecord promptRecord = promptRecordMapper.selectOne(qw);
        if (!Objects.isNull(promptRecord)) {
            if (promptRecord.getGenEndTime() != null) {
                return true;
            }
            if (promptRecord.getFailureMessage() != null) {
                return true;
            }
            if (promptRecord.getDel()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 处理任务图像数据（简化版本，不包含图片压缩处理）
     */
    private void processTaskImages(String promptId, String loginName, MidjourneyResponse.TaskData taskData) {
        List<String> images = taskData.getImages();
        String markId = redisService.stringGet(promptId);

        // 更新用户当日生图数量
        Integer userTodayCreateImgNums = (Integer) redisService.getDataFromHash(USER_TODAY_CREATE_IMG_NUMS, loginName);
        userTodayCreateImgNums = !Objects.isNull(userTodayCreateImgNums) ? userTodayCreateImgNums : 0;
        redisService.putDataToHash(USER_TODAY_CREATE_IMG_NUMS, loginName, userTodayCreateImgNums + 4);

        // 保存基础图像文件信息到gpt_prompt_file表（压缩图片URL将通过MQ消息处理后更新）
        for (String imageUrl : images) {
            PromptFile promptFile = new PromptFile();
            promptFile.setLoginName(loginName);
            promptFile.setPromptId(promptId);

            // 从URL中提取文件名
            String fileName = extractFileNameFromUrl(imageUrl);
            promptFile.setFileName(fileName);

            // 暂时保存原始图片URL，压缩图片URL将通过MQ消息处理后更新
            promptFile.setFileUrl(imageUrl);
            promptFile.setThumbnailUrl(imageUrl); // 临时使用原图URL
            promptFile.setHighThumbnailUrl(imageUrl); // 临时使用原图URL

            // 设置图片尺寸
            if (taskData.getWidth() != null && taskData.getHeight() != null) {
                promptFile.setWidth(taskData.getWidth() / 2);
                promptFile.setHeight(taskData.getHeight() / 2);
            } else {
                // 默认Midjourney图片尺寸
                promptFile.setWidth(1024);
                promptFile.setHeight(1024);
            }

            promptFile.setCreateTime(LocalDateTime.now());
            promptFile.setCreateBy(loginName);

            promptFileMapper.insert(promptFile);
            log.info("Saved image file for jobId: {}, imageUrl: {}", promptId, imageUrl);
        }

        // 更新VIP相关统计，参考GenService的callBack方法
        try {
            vipService.updateMessageByMarkId(markId, promptId, loginName, images.size(), images.size(), images.size());
        } catch (Exception e) {
            log.error("更新VIP统计信息失败，jobId: {}, user: {}", promptId, loginName, e);
        }

        log.info("Processed {} images for jobId: {}, user: {}", images.size(), promptId, loginName);
    }

    /**
     * 发送图片处理MQ消息
     */
    private void sendImageProcessMessage(String jobId, String loginName, MidjourneyResponse.TaskData taskData) {
        try {
            String markId = redisService.stringGet(jobId);
            List<String> images = taskData.getImages();

            // 构建图片处理信息列表
            List<MjImageProcessVo.ImageInfo> imageInfos = new ArrayList<>();
            for (String imageUrl : images) {
                MjImageProcessVo.ImageInfo imageInfo = new MjImageProcessVo.ImageInfo();
                imageInfo.setOriginalUrl(imageUrl);
                imageInfo.setFileName(extractFileNameFromUrl(imageUrl));

                // 设置图片尺寸
                if (taskData.getWidth() != null && taskData.getHeight() != null) {
                    imageInfo.setWidth(taskData.getWidth() / 2);
                    imageInfo.setHeight(taskData.getHeight() / 2);
                } else {
                    // 默认Midjourney图片尺寸
                    imageInfo.setWidth(1024);
                    imageInfo.setHeight(1024);
                }

                // 注意：这里不设置promptFileId，因为PromptFile还没有插入数据库
                // 图片处理服务需要根据jobId和imageUrl来查找对应的PromptFile记录
                imageInfos.add(imageInfo);
            }

            MjImageProcessVo processVo = new MjImageProcessVo();
            processVo.setJobId(jobId);
            processVo.setLoginName(loginName);
            processVo.setMarkId(markId);
            processVo.setImageInfos(imageInfos);
            processVo.setNsfwCheck(false);

            CommonMqMessage<MjImageProcessVo> mqMessage = new RMqMessage<>(
                    mjImageProcessTopic,
                    mjImageProcessTag,
                    jobId + "_image_process"
            );
            mqMessage.setMessage(processVo);

            normalMessageProducer.syncSend(mqMessage);

            log.info("Sent image process message for jobId: {}, imageCount: {}", jobId, imageInfos.size());
        } catch (Exception e) {
            log.error("Failed to send image process message for jobId: {}", jobId, e);
        }
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFileNameFromUrl(String imageUrl) {
        if (StringUtil.isBlank(imageUrl)) {
            return "midjourney_image.png";
        }

        try {
            String[] parts = imageUrl.split("/");
            String lastPart = parts[parts.length - 1];

            // 移除查询参数
            if (lastPart.contains("?")) {
                lastPart = lastPart.substring(0, lastPart.indexOf("?"));
            }

            return StringUtil.isNotBlank(lastPart) ? lastPart : "midjourney_image.png";
        } catch (Exception e) {
            log.warn("Failed to extract filename from URL: {}", imageUrl);
            return "midjourney_image.png";
        }
    }

    /**
     * 清理Redis任务信息
     */
    public void cleanupTaskFromRedis(String jobId, String loginName) {
        try {
            // 获取markId
            String markId = redisService.stringGet(jobId);

            // 清理MJ任务状态（模拟传统任务的清理方式）
            if (StringUtil.isNotBlank(markId)) {
                // 从用户hash中删除任务状态(重要，删除之后，前端轮询会直接认定为任务完成进行最终处理然后删除redisService.delete(originPromptRecord.getMarkId())，不需要-2状态)
                redisService.deleteFieldFromHash(loginName, markId);

                // 删除markId -> loginName映射，先别删除，轮询时候再去删除
//                redisService.delete(markId);

                // 删除任务时间戳
                redisService.delete(USER_TASK_TIMESTAMP + markId);

                log.debug("Cleaned up MJ task status for markId: {}", markId);
            }

            // 删除jobId -> markId映射
            redisService.delete(jobId);

            // 删除任务信息
            String taskKey = TTAPI_MJ_TASK_PREFIX + jobId;
            redisService.delete(taskKey);

            // 删除图片处理锁-防止重复处理
            String imgKey = TTAPI_MJ_IMG_PREFIX + jobId;
            redisService.delete(imgKey);

            // 从并发任务列表中删除
            removeMidjourneyConcurrentJob(loginName, jobId);

            // 刷新预扣任务
            lumenService.notFinishTask(loginName);
            log.info("Cleaned up all Redis data for jobId: {}, markId: {}", jobId, markId);

        } catch (Exception e) {
            log.error("Error cleaning up task from redis for jobId: " + jobId, e);
        }
    }

    public void checkMidjourneyBalance() {
        try {
            Call<MidjourneyResponse> call = midjourneyApiClient.info(midjourneyConfig.getApiKey());
            Response<MidjourneyResponse> response = call.execute();
            log.info("Midjourney info API call result, response: {}", response);
            if (!response.isSuccessful()) {
                throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
            }

            MidjourneyResponse midjourneyResponse = response.body();
            log.info("Midjourney info API call infoResponse: {}", midjourneyResponse);
            if (midjourneyResponse == null || midjourneyResponse.getData() == null) {
                throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
            }
            MidjourneyResponse.InfoResponse infoResponse = JsonUtils.convertValue(midjourneyResponse.getData(), MidjourneyResponse.InfoResponse.class);
            if (infoResponse.getBalance().compareTo(new BigDecimal(midjourneyConfig.getBalanceAlarmThreshold())) < 0) {
                //余额不足，告警
                dingTalkAlert.sendAlarmMessage(LogicUtil.buildAlarmMessage(
                        AlarmEnum.AlarmTypeEnum.BUSINESS.getDescription(),
                        AlarmEnum.AlarmSourceEnum.MIDJOURNEY_BALANCE.getDescription(),
                        "Midjourney余额不足，当前余额：" + infoResponse.getBalance() + "，告警阈值：" + midjourneyConfig.getBalanceAlarmThreshold(),
                        null,
                        null)
                );
            }
        } catch (Exception e) {
            log.error("Check midjourney balance error", e);
        }
    }
}
