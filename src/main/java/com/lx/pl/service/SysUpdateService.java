package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lx.pl.db.mysql.community.entity.*;
import com.lx.pl.db.mysql.gen.entity.GptSysUpdate;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.GptSysUpdateMapper;
import com.lx.pl.db.mysql.gen.repository.UserSysUpdateRepository;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.exception.ServerInternalException;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SysUpdateService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private GptSysUpdateMapper gptSysUpdateMapper;

    @Autowired
    private UserSysUpdateRepository userSysUpdateRepository;

    @Autowired
    private RedissonClient redissonClient;

    public Boolean readSysUpdate(String sysUpdateId, User user, String platform) {
        RLock lock = redissonClient.getLock("read:sysUpdate:" + user.getId());
        try {
            //单个已读系统更新
            if (StringUtil.isNotBlank(sysUpdateId)) {
                UserSysUpdate userSysUpdate = getUserSysUpdate(user.getId(), sysUpdateId);
                //判断用户是否已读系统更新
                if (Objects.isNull(userSysUpdate)) {
                    UserSysUpdate sysUpdate = new UserSysUpdate();

                    //已读发起者用户
                    AccountInfo accountInfo = new AccountInfo();
                    accountInfo.setUserId(user.getId());
                    accountInfo.setUserName(user.getUserName());
                    accountInfo.setUserLoginName(user.getLoginName());
                    accountInfo.setUserAvatarUrl(user.getAvatarUrl());
                    accountInfo.setWhetherPro(Boolean.FALSE);
                    sysUpdate.setAccountInfo(accountInfo);

                    sysUpdate.setSysUpdateId(sysUpdateId);
                    sysUpdate.setPlatform(platform);
                    sysUpdate.setCreateTime(LocalDateTime.now());

                    userSysUpdateRepository.insert(sysUpdate);
                }
            } else {
                //全部更新为已读
                LambdaQueryWrapper<GptSysUpdate> lwq = new LambdaQueryWrapper<>();
                lwq.eq(GptSysUpdate::getPublish, Boolean.TRUE);
                lwq.eq(GptSysUpdate::getPlatform, platform);
                List<GptSysUpdate> gptsysUpdateList = gptSysUpdateMapper.selectList(lwq);

                if (!CollectionUtils.isEmpty(gptsysUpdateList)) {
                    List<String> gptsysUpdateIdList = gptsysUpdateList.stream()
                            .map(gptSysUpdate -> String.valueOf(gptSysUpdate.getId()))
                            .collect(Collectors.toList());

                    //移除所有的已读数据
                    Query query = new Query();
                    query.addCriteria(Criteria.where("accountInfo.userId").is(user.getId()));
                    // 执行删除操作并判断是否删除了至少一个文档
                    mongoTemplate.remove(query, UserSysUpdate.class);

                    //批量插入所有系统更新数据
                    List<UserSysUpdate> userSysUpdateList = new ArrayList<>();
                    for (String gptSysUpdateId : gptsysUpdateIdList) {
                        UserSysUpdate userSysUpdate = new UserSysUpdate();

                        //已读发起者用户
                        AccountInfo accountInfo = new AccountInfo();
                        accountInfo.setUserId(user.getId());
                        accountInfo.setUserName(user.getUserName());
                        accountInfo.setUserLoginName(user.getLoginName());
                        accountInfo.setUserAvatarUrl(user.getAvatarUrl());
                        accountInfo.setWhetherPro(Boolean.FALSE);
                        userSysUpdate.setAccountInfo(accountInfo);

                        userSysUpdate.setSysUpdateId(gptSysUpdateId);
                        userSysUpdate.setPlatform(platform);
                        userSysUpdate.setCreateTime(LocalDateTime.now());

                        userSysUpdateList.add(userSysUpdate);
                    }
                    userSysUpdateRepository.saveAll(userSysUpdateList);
                }
            }

            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("用户：{} 对系统更新：{} 已读报错", user.getId(), sysUpdateId, e);
            throw new ServerInternalException("Read SysUpdate Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public UserSysUpdate getUserSysUpdate(Long userId, String sysUpdateId) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("accountInfo.userId").is(userId));
            query.addCriteria(Criteria.where("sysUpdateId").is(sysUpdateId));
            UserSysUpdate userSysUpdate = mongoTemplate.findOne(query, UserSysUpdate.class);
            return userSysUpdate;
        } catch (Exception e) {
            log.error("查询用户已读系统更新报错，系统更新id为：{}，用户id:{}", sysUpdateId, userId, e);
            return null;
        }
    }


    public CommPageInfo<GptSysUpdate> getSysUpdateMessage(String lastSysUpdateId, Integer pageSize, User user, String platform) {
        List<GptSysUpdate> gptSysUpdateList = new ArrayList<>();
        try {
            LambdaQueryWrapper<GptSysUpdate> lqw = new LambdaQueryWrapper<>();

            lqw.eq(GptSysUpdate::getPublish, true); // 仅查询已发布的数据
            if (StringUtil.isNotBlank(lastSysUpdateId)) {
                lqw.lt(GptSysUpdate::getId, lastSysUpdateId); // 仅查询 id 小于 lastId 的数据（游标分页）
            }

            if (StringUtil.isNotBlank(platform)){
                lqw.eq(GptSysUpdate::getPlatform, platform);
            }

            lqw.orderByDesc(GptSysUpdate::getId); // 按 id 倒序排序
            lqw.last("LIMIT " + pageSize); // 限制查询条数

            gptSysUpdateList = gptSysUpdateMapper.selectList(lqw);

            //组装图片的地址
            if (!CollectionUtils.isEmpty(gptSysUpdateList)) {
                List<String> sysUpdateMessageIds = gptSysUpdateList.stream()
                        .map(gptSysUpdate -> String.valueOf(gptSysUpdate.getId()))
                        .collect(Collectors.toList());
                Set<String> sysUpdateIdSet = getReadSysUpdateByUser(sysUpdateMessageIds, user);

                for (GptSysUpdate gptSysUpdate : gptSysUpdateList) {
                    gptSysUpdate.setRead((!Objects.isNull(sysUpdateIdSet) && sysUpdateIdSet.contains(String.valueOf(gptSysUpdate.getId()))) ? Boolean.TRUE : Boolean.FALSE);
                }
            }

        } catch (Exception e) {
            log.error("获取个人通知表报错", e);
            throw new ServerInternalException("Error retrieving the SysUpdate list");
        }
        return buildPromptPageInfo(pageSize, gptSysUpdateList);
    }


    public Set<String> getReadSysUpdateByUser(List<String> sysUpdateMessageIds, User user) {
        try {
            Set<String> sysUpdateIdSet = new HashSet<>();

            Query query = new Query();
            query.addCriteria(Criteria.where("accountInfo.userId").in(user.getId())
                    .and("sysUpdateId").in(sysUpdateMessageIds));
            List<UserSysUpdate> sysUpdateIdList = mongoTemplate.find(query, UserSysUpdate.class);

            if (!CollectionUtils.isEmpty(sysUpdateIdList)) {
                sysUpdateIdSet = sysUpdateIdList.stream()
                        .map(UserSysUpdate::getSysUpdateId).collect(Collectors.toSet());
            }
            return sysUpdateIdSet;
        } catch (Exception e) {
            log.error("查询用户：{} 的已读系统公告报错", user.getLoginName(), e);
            return null;
        }
    }


    // 构建分页结果对象
    private CommPageInfo<GptSysUpdate> buildPromptPageInfo(Integer pageSize, List<GptSysUpdate> gptSysUpdateList) {
        CommPageInfo<GptSysUpdate> commPageInfo = new CommPageInfo<>();
        commPageInfo.setResultList(CollectionUtils.isEmpty(gptSysUpdateList) ? Collections.emptyList() : gptSysUpdateList);
        commPageInfo.setPageSize(pageSize);
        commPageInfo.setLastId(CollectionUtils.isEmpty(gptSysUpdateList) ? "" : String.valueOf(gptSysUpdateList.get(gptSysUpdateList.size() - 1).getId()));
        return commPageInfo;
    }

    public List<UserSysUpdate> getAllReadSysUpdateByUser(User user) {
        try {
            Set<String> sysUpdateIdSet = new HashSet<>();

            Query query = new Query();
            query.addCriteria(Criteria.where("accountInfo.userId").in(user.getId()));
            List<UserSysUpdate> sysUpdateIdList = mongoTemplate.find(query, UserSysUpdate.class);

            return sysUpdateIdList;
        } catch (Exception e) {
            log.error("查询用户：{} 的已读系统公告报错", user.getLoginName(), e);
            return null;
        }
    }
}
