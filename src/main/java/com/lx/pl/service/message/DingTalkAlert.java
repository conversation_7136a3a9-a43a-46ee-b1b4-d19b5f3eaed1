package com.lx.pl.service.message;

import cn.hutool.core.date.DateUtil;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.lx.pl.dto.alarm.AlarmDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

import static com.lx.pl.interceptor.LoggingInterceptor.TRACE_ID;

@Slf4j
@Service
public class DingTalkAlert {
    @Value("${spring.profiles.active:default}")
    private String env;
    @Resource(name = "dingTalkAlarmExecutor")
    private ThreadPoolTaskExecutor dingTalkAlarmExecutor;
    public static final String COMMON_BUSINESS_ALARM_TOKEN = "4d12156fe8b4438e8d862a8b688813c471e710010bea0da5c2ba4cebb9d15502";
    private static final StackWalker STACK_WALKER = StackWalker.getInstance(
            Set.of(StackWalker.Option.RETAIN_CLASS_REFERENCE,
                    StackWalker.Option.SHOW_HIDDEN_FRAMES)
    );
    private final String currentClassName = DingTalkAlert.class.getName();

    public void sendAlarmMessage(AlarmDTO alarmDTO) {
        if (alarmDTO == null) {
            return;
        }
        try {
            alarmDTO.setTraceId(MDC.get(TRACE_ID));
            STACK_WALKER.walk(frames -> {
                StackWalker.StackFrame target = frames
                        .skip(1)
                        .filter(f -> !f.getClassName().equals(currentClassName))
                        .filter(f -> !f.getClassName().contains("$Proxy"))
                        .findFirst()
                        .orElse(null);

                if (target != null) {
                    alarmDTO.setClazz(target.getDeclaringClass().getSimpleName());
                    alarmDTO.setMethod(target.getMethodName());
                }

                return alarmDTO;
            });
            CompletableFuture.runAsync(() -> {
                try {
                    //构造告警信息
                    String alarmMessage = alarmDTO.getType() + "\n"
                            + "环境：" + env + "\n"
                            + "发生时间：" + DateUtil.formatLocalDateTime(LocalDateTime.now()) + "\n"
                            + "告警类型：" + alarmDTO.getSource() + "\n"
                            + "告警类：" + alarmDTO.getClazz() + "\n"
                            + "告警方法：" + alarmDTO.getMethod() + "\n"
                            + "告警详情：" + alarmDTO.getDetail() + "\n"
                            + "trace_id：" + alarmDTO.getTraceId();
                    if (StringUtils.isNotBlank(alarmDTO.getUserInfo())) {
                        alarmMessage += "\n用户信息：" + alarmDTO.getUserInfo();
                    }

                    if (StringUtils.isNotBlank(alarmDTO.getExceptionClassName()) && alarmDTO.getExceptionLineNum() != null) {
                        alarmMessage += "\n异常类型：" + alarmDTO.getExceptionClassName();
                        alarmMessage += "\n异常方法行数：" + alarmDTO.getExceptionLineNum();
                    }
                    DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/robot/send");
                    OapiRobotSendRequest req = new OapiRobotSendRequest();
                    //定义文本内容
                    OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
                    text.setContent(alarmMessage);
                    //设置消息类型
                    req.setMsgtype("text");
                    req.setText(text);
                    client.execute(req, COMMON_BUSINESS_ALARM_TOKEN);
                } catch (Exception e) {
                    log.error("发送钉钉告警信息异常, ", e);
                }
            }, dingTalkAlarmExecutor);
        } catch (Exception e) {
            log.error("处理钉钉告警信息异常, ", e);
        }
    }
}
