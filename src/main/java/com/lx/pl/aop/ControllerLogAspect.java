package com.lx.pl.aop;

import com.lx.pl.dto.generic.R;
import com.lx.pl.util.DateUtils;
import com.lx.pl.util.IpUtils;
import com.lx.pl.util.JsonUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

//@Aspect
//@Component
public class ControllerLogAspect {
    private static final Logger logger = LoggerFactory.getLogger("action-task");

    private static final String AUTHORIZATION = "authorization";
    private static final String PLATFORM = "Platform";

    // 定义切点，拦截所有Controller包下的方法
    @Pointcut("execution(* com.lx.pl.controller..*.*(..)) " +
            "&& !within(com.lx.pl.controller.optionLog.activity.UserActivityController)" +
            "&& !within(com.lx.pl.controller.community.CommImgController)")
    public void controllerPointCut() {
    }

    // 在方法执行前记录请求信息
    @Before("controllerPointCut()")
    public void logRequest(JoinPoint joinPoint) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                return;
            }
            HttpServletRequest request = attributes.getRequest();

            // 打印请求信息
            logger.info("Request =>  DT:{}, URL: {}, Method: {}, Headers: {}, Params: {}, Client IP: {}",
                    DateUtils.getTime(),
                    request.getRequestURL(),
                    request.getMethod(),
                    getHeadersInfo(request),
                    JsonUtils.writeToString(joinPoint.getArgs()),
                    IpUtils.getIpAddr(request));
        } catch (Exception e) {
        }

    }

    // 方法返回后打印日志
    @AfterReturning(pointcut = "controllerPointCut()", returning = "result")
    public void afterReturning(JoinPoint joinPoint, Object result) {
        try {
            String className = joinPoint.getTarget().getClass().getName();
            String methodName = joinPoint.getSignature().getName();

            // 打印响应信息
            if (result instanceof R) {
                R<?> r = (R<?>) result;
                logger.info("Response <= Method:{}.{}, Status: {}, Message: {}, Data: {}",
                        className, methodName,
                        r.getStatus(),
                        r.getMessage(),
                        JsonUtils.writeToString(r.getData()));
            } else {
                logger.info("Response <=Method:{}.{}, Result: {}",
                        className, methodName,
                        JsonUtils.writeToString(result));
            }
        } catch (Exception e) {
        }
    }

    private Map<String, String> getHeadersInfo(HttpServletRequest request) {
        Map<String, String> headers = new HashMap<>();
        headers.put(PLATFORM, request.getHeader(PLATFORM));
        headers.put(AUTHORIZATION, request.getHeader(AUTHORIZATION));
        return headers;
    }
}
