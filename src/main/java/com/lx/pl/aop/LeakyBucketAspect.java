package com.lx.pl.aop;

import com.lx.pl.annotation.LeakyBucketLimit;
import com.lx.pl.dto.RequestQueueParams;
import com.lx.pl.dto.generic.R;
import com.lx.pl.exception.BadRequestException;
import com.lx.pl.service.RequestQueueService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

@Aspect
@Component
public class LeakyBucketAspect {


    // 存储每个方法的独立限流实例
    private final ConcurrentHashMap<String, RequestQueueService> rateLimiters = new ConcurrentHashMap<>();

    @Around("@annotation(com.lx.pl.annotation.LeakyBucketLimit)")
    public Object rateLimit(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        LeakyBucketLimit leakyBucketLimit = method.getAnnotation(LeakyBucketLimit.class);

        // 获取方法唯一标识（类名 + 方法名）
        String methodKey = method.getDeclaringClass().getName() + "." + method.getName();

        RequestQueueParams requestQueueParams = new RequestQueueParams();
        requestQueueParams.setQueueSize(leakyBucketLimit.queueSize());
        requestQueueParams.setInterval(leakyBucketLimit.intervalSeconds());
        // 获取或创建该方法的限流队列
        RequestQueueService requestQueueService = rateLimiters.computeIfAbsent(methodKey, k ->
                new RequestQueueService(requestQueueParams)
        );

        // 创建异步任务
        CompletableFuture<Object> futureResponse = new CompletableFuture<>();
        Runnable requestTask = () -> {
            try {
                Object response = joinPoint.proceed();
                futureResponse.complete(response);
            } catch (Throwable throwable) {
                futureResponse.completeExceptionally(throwable);
            }
        };

        // 尝试加入队列
        CompletableFuture<Boolean> addedToQueueFuture = requestQueueService.addRequest(requestTask);
        boolean addedToQueue = addedToQueueFuture.join();

        // 如果队列已满，抛出限流异常
        if (!addedToQueue) {
            throw new BadRequestException("Rate limit exceeded. Please try again later !");
        }

        // 阻塞等待任务完成并获取结果
        return futureResponse.join();
    }
}
