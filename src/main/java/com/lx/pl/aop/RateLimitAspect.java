package com.lx.pl.aop;

import com.lx.pl.annotation.RateLimit;
import com.lx.pl.dto.RateLimiter;
import com.lx.pl.exception.BadRequestException;
import com.lx.pl.exception.LogicDoException;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;

@Aspect
@Component
public class RateLimitAspect {
    private final ConcurrentHashMap<String, RateLimiter> rateLimiterMap = new ConcurrentHashMap<>();

    @Before("@annotation(rateLimit)")
    public void checkRateLimit(RateLimit rateLimit) throws Throwable {
        String key = generateKey();  // 生成限流的 key，可以根据具体业务场景来定（如 IP 地址、用户ID等）

        RateLimiter rateLimiter = rateLimiterMap.computeIfAbsent(key, k -> new RateLimiter(rateLimit.limit(), rateLimit.timeout()));

        if (!rateLimiter.tryAcquire()) {
            throw new LogicDoException("The request is too frequent, please try again later!");
        }
    }

    private String generateKey() {
        // 生成一个唯一的 key，可以基于用户ID、IP地址或者方法名等
        return "rate_limit_unique_key";
    }
}
