package com.lx.pl.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/16
 * @description 告警相关枚举
 */
public class AlarmEnum {
    @Getter
    public enum AlarmTypeEnum {
        BUSINESS("Piclumen业务告警"),
        EXCEPTION("Piclumen异常告警"),
        ;

        private final String description;

        AlarmTypeEnum(String description) {
            this.description = description;
        }
    }

    @Getter
    public enum AlarmSourceEnum {
        PUSH("PUSH推送告警"),
        MIDJOURNEY_BALANCE("Midjourney余额告警"),
        PAY_PAL("PayPal告警"),
        ;

        private final String description;

        AlarmSourceEnum(String description) {
            this.description = description;
        }
    }
}
