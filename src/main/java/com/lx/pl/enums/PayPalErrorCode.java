package com.lx.pl.enums;

/**
 * @Description: paypal支付处理枚举，描述不返回前端，用于细分错误码，message 描述前端显示错误
 * @Author: senlin_he
 * @Date: 2025/2/27
 */
public enum PayPalErrorCode {
    INVALID_REQUEST_PARAMETERS("910", "Invalid request parameters: planId or customId cannot be null.", "参数错误"),
    SUBSCRIPTION_CREATION_FAILED("911", "Failed to create subscription.", "创建订阅失败"),
    CALL_PAYPAL_API_ERROR("912", "Failed to call paypal api .", "调用paypal支付异常"),
    SUBSCRIPTION_NOT_FOUND("913", "Subscription not found.", "没有订阅"),
    SUBSCRIPTION_REQUIRED("914", "You must subscribe before purchasing Lumen.", "订阅后才能购买"),
    NO_PAYPAL_PRODUCT_FOUND("915", "Product not found.", "没有找到db对应的PaypalProduct数据"),
    ACTIVE_SUBSCRIPTION_REQUIRED("916", "You must subscribe before.", "没有激活的请阅"),
    PLAN_NOT_FOUND("917", "Plan not found.", "没有找到db对应的PaypalPlan数据"),
    CANCEL_SUBSCRIPTION_FAILED("918", "Failed to cancel subscription.", "取消订阅失败"),
    UNKNOWN_UPDATE_TYPE("919", "Invalid update parameters.", "不支持的更新类型（next_billing_period,immediate）"),
    CANNOT_UPGRADE_SUBSCRIPTION("920", "Cannot upgrade subscription", "升级规则不满足，不能切换指定订阅"),
    CANNOT_DOWNGRADE_SUBSCRIPTION("921", "Cannot downgrade subscription", "降级规则不满足，不能切换指定订阅"),
    DOWNGRADE_FAILED("922", "Failed to upgrade/downgrade subscription.", "升级/降级订阅失败"),
    UNSUPPORTED_PAYMENT_TYPE("923", "Invalid parameters.", "不支持的payment_type，只支持one和plan"),
    NOT_FOUND_LAST_PAYMENT_ID("924", "Not found last payment id.", "没有找到上一次支付的记录"),
    CROSS_PLATFORM_ERROR("930", "Cannot make cross-platform payments.", "不能够跨平台支付"),
    UPGRADE_FAILED_WITHOUT_PAYMENT("933", "Failed to upgrade subscription, not found last paymentID.", "升级订阅失败"),
    UNKNOWN_ERROR("999", "paypal unknown error.", "paypal未知异常"),
    REVISE_SUBSCRIPTION_EXIST("931", "Failed to revise subscription.", "已存在未来的升降级计划、不能进行操作");

    private final String code;
    private final String message;
    private final String description;

    PayPalErrorCode(String code, String message, String description) {
        this.code = code;
        this.message = message;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public String getDescription() {
        return description;
    }
}
