package com.lx.pl.enums;

public enum PlatformUserTypeEnum {

    vip("vip", "会员"),
    not_vip("not_vip", "非vip"),
    all("all", "所有用户"),;

    private String value;

    private String label;

    //如果枚举值中还有数据则必须要创建一个构造函数
    PlatformUserTypeEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }


    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
