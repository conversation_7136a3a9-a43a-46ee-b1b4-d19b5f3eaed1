package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "批量收藏夹操作 前端传入参数")
public class FileCollectBatch {

    @Schema(description = "收藏夹id")
    private String classifyId;

    @Schema(description = "老收藏夹id(用于收藏移动)")
    private String oldClassifyId;

    @Schema(description = "批量收藏列表")
    List<CollectBatch> collectBatchList;
}
