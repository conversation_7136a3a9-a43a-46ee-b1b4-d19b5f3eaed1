package com.lx.pl.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class VaryParams {

    @Schema(description = "图片地址")
    private String img_url;

    @Schema(description = "回调地址")
    private String callback_url;

    @Schema(description = "正向提示词")
    private String prompt;

    @Schema(description = "反向提示词")
    private String negative_prompt;

    @Schema(description = "标记id")
    private String mark_id;

    @Schema(description = "模型id")
    private String model_id;

    @Schema(description = "服务器地址id")
    private String address;

    @Schema(description = "图片变化的程度配置 subtle:0.55  strong:0.85")
    private String strength;

    @Schema(description = "生成图片数量")
    private Integer batch_size;

    @Schema(description = "是否包含提示词： ‘’:提示词不为空  img_vary_wd : 提示词为空")
    private String flag;

    @Schema(description = "是否开启模型加速，默认为：false")
    private Boolean accelerated;
}
