package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ServerStatus {
    @Schema(description = "服务器id")
    private String id;

    @Schema(description = "是否能分配到当前服务器")
    private Boolean flag;

    @Schema(description = "服务器地址")
    private String address;

    @Schema(description = "服务器部署的模型")
    private List<String> tags;

    @Schema(description = "服务器正在排队数")
    private Integer queue_num;
}
