package com.lx.pl.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class UserLumens {

    @Schema(description = "每日免费点数日期(只记录年月日)")
    private LocalDateTime dailyLumensTime;

    @Schema(description = "每日免费点数")
    private Integer dailyLumens;

    @Schema(description = "会员点数")
    private Integer vipLumens;

    @Schema(description = "充值点数")
    private Integer rechargeLumens;

    @Schema(description = "赠送点数")
    private Integer giftLumens;

    @Schema(description = "剩余每日免费点数")
    private Integer leftDailyLumens;

    @Schema(description = "剩余会员点数")
    private Integer leftVipLumens;

    @Schema(description = "剩余充值点数")
    private Integer leftRechargeLumens;

    @Schema(description = "剩余赠送点数")
    private Integer leftGiftLumens;

    @Schema(description = "剩余总的点数")
    private Integer leftTotalLumens;
}
