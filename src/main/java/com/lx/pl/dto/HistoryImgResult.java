package com.lx.pl.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.JsonNode;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class HistoryImgResult {
    /**
     * 用户名称
     */
    private String loginName;

    private String promptId;

    /**
     * 文件名称
     */
    private String imgName;


    /**
     * 缩略图名称
     */
    private String thumbnailName;

    /**
     * 高清缩略图名称
     */
    private String highThumbnailName;

    /**
     * 文件路径
     */
    private String imgUrl;

    /**
     * 缩略图路径
     */
    private String thumbnailUrl;

    /**
     * 高清缩略图路径
     */
    private String highThumbnailUrl;

    /**
     * 小图路径
     */
    private String miniThumbnailUrl;

    /**
     * 30% 高清图
     */
    private String highMiniUrl;

    /**
     * 敏感信息：涉黄等
     */
    private String sensitiveMessage;

    /**
     * 生成图片的宽
     */
    private int realWidth;

    /**
     * 生成图片的高
     */
    private int realHeight;

    /**
     * 正向提示词
     */
    private String prompt;

    /**
     * 生图信息
     */
    private JsonNode genInfo;

    /**
     * 生图原始操作
     */
    private String originCreate;

    /**
     * 是否被删除：0：未删除 1：已删除
     */
    private boolean del;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    @Schema(description = "是否公开(0:未公开 1:已公开 2:审核中  3.已拒绝)")
    private Integer isPublic;

    @Schema(description = "拒绝内容描述")
    private String rejectionContent;

    /**
     * 收藏夹id
     */
    private Long classifyId;

    public boolean getDel() {
        return del;
    }

    public void setDel(boolean del) {
        this.del = del;
    }
}
