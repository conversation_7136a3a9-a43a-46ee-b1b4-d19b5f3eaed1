package com.lx.pl.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DealImgParams {

    @Schema(description = "图片路径")
    private String imgUrl;

    @Schema(description = "生图id")
    private String promptId;

    @Schema(description = "高清修复指数")
    private double scale;

    @Schema(description = "去噪指数")
    private double denoise;

    @Schema(description = "生图用户")
    private String loginName;

    @Schema(description = "模型id")
    private String modelId;

    @Schema(description = "忽略告警，继续生图")
    private Boolean continueCreate;

}
