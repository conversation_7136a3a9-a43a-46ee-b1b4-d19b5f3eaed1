package com.lx.pl.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class RembgParams {

    @Schema(description = "模型类型")
    private String type;

    @Schema(description = "图片地址")
    private String img_url;

    @Schema(description = "标记id")
    private String mark_id;

    @Schema(description = "或掉地址")
    private String callback_url;

    private String prompt_id;

    @Schema(description = "服务器地址id")
    private String address;

    @Schema(description = "是否开启模型加速，默认为：false")
    private Boolean accelerated;

}
