package com.lx.pl.dto.alarm;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/16
 * @description
 */
@Data
@Builder
public class AlarmDTO {
    /**
     * 告警类型
     */
    private String type;

    /**
     * 告警来源
     */
    private String source;

    /**
     * 告警类
     */
    private String clazz;

    /**
     * 告警方法
     */
    private String method;

    /**
     * 详情
     */
    private String detail;

    /**
     * 异常类信息
     */
    private String exceptionClassName;

    /**
     * 异常代码行数
     */
    private Integer exceptionLineNum;

    /**
     * 用户信息，id或者loginName
     */
    private String userInfo;

    /**
     * 链路id
     */
    private String traceId;
}
