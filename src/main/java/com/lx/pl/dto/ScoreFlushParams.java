package com.lx.pl.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScoreFlushParams {
    private String commFileId;

    private Integer interactionScore;

    public ScoreFlushParams() {
    }

    public ScoreFlushParams(String commFileId, Integer interactionScore) {
        this.commFileId = commFileId;
        this.interactionScore = interactionScore;
    }
}
