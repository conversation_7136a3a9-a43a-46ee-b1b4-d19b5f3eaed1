package com.lx.pl.dto.generic;

/**
 * 请求响应返回基础类
 *
 * @param <T>
 */
public class R<T> {

    //返回状态码
    private Integer status;
    //返回消息
    private String message;
    //返回数据
    private T data;

    //Success结果
    public static <T> R<T> success(T data) {
        R<T> resultData = new R<>();
        resultData.setStatus(0);
        resultData.setMessage("success");
        resultData.setData(data);
        return resultData;
    }

    //Fail结果
    public static <T> R<T> fail(int code, String message) {
        R<T> resultData = new R<>();
        resultData.setStatus(code);
        resultData.setMessage(message);
        return resultData;
    }

    public static <T> R<T> success() {
        R<T> resultData = new R<>();
        resultData.setStatus(0);
        resultData.setMessage("success");
        return resultData;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
