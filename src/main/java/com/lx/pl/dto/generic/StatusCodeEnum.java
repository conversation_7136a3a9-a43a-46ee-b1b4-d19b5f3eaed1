package com.lx.pl.dto.generic;

/**
 * <AUTHOR>
 */
public enum StatusCodeEnum {
    SUCCESS(200, "Success"),
    UPLOAD_LIMIT(6001, "Upload limit !"),
    ;
    private Integer code;

    private String desc;

    //如果枚举值中还有数据则必须要创建一个构造函数
    StatusCodeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    // 根据错误码获取错误信息
    public static String getErrorMessage(int code) {
        for (StatusCodeEnum errorCode : StatusCodeEnum.values()) {
            if (errorCode.getCode() == code) {
                return errorCode.getDesc();
            }
        }
        return "Unknown error code: " + code;
    }
}
