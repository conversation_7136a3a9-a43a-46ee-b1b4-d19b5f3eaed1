package com.lx.pl.dto;

import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class TaskQueueParams {

    @Schema(description = "用户生图入参")
    private JsonNode genInfo;

    @Schema(description = "用户生图入参: ttp:文生图 ptp:图生图 upscale:超分 inpaint:局部重绘 expand:扩图 removeBg:去背景 lineRecolor:线稿上色 ")
    private String featuresTypeValue;

}
