package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "用户收藏返回参数")
public class CollectUseSize {

    @Schema(description = "本次改变size")
    private  Long size;

    @Schema(description = "本次操作用户使用总size")
    private  Long usedSize;

    @Schema(description = "用户总size")
    private  Long totalSize;

    @Schema(description = "本次更新num")
    private Integer num;

    @Schema(description = "用户已收藏数量")
    private Integer usedCollectNum;

    @Schema(description = "用户能收藏总数量")
    private Integer totalCollectNum;
}
