package com.lx.pl.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lx.pl.db.mysql.community.entity.AccountInfo;
import com.lx.pl.db.mysql.community.entity.Challenge;
import lombok.Data;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@ToString
@Data
public class CommHistoryFile {

    private Long exploreId;

    /**
     * 图片点赞数
     */
//    @Indexed
    private Integer fileLikeNums = 0;

    /**
     * 图片评论数
     */
    private Integer fileCommentNums = 0;

    /**
     * 文件id，原来mysql对应的id，不应该以这个字段作为处理
     */
    private String promptFileId;

    /**
     * 文件路径
     */
    private String fileUrl;

    /**
     * 缩略图路径
     */
    private String thumbnailUrl;

    /**
     * 高清缩略图路径
     */
    private String highThumbnailUrl;

    /**
     * 标签
     */
//    @Indexed
    private List<String> tags = new ArrayList<>();

    /**
     * 发布社区时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;


    private Long userId;
    /**
     * 用户昵称
     */
    private String userName;

    /**
     * 用户账号
     */
//    @Indexed
    private String userLoginName;

    /**
     * 头像文件路径
     */
    private String userAvatarUrl;

    /**
     * 生图入参
     */
    private String genInfo;

    /**
     * 图片结果宽度
     */
    private Integer realWidth;

    /**
     * 图片结果高度
     */
    private Integer realHeight;

    /**
     * 生图提示词
     */
    private String prompt;

    /**
     * 图片描述
     */
    private String describe = "";

    /**
     * 生图挑战赛
     */
    private Challenge challenge = new Challenge();

    /**
     * 公开类型：everyone ： 所有人可见  myself ： 自己可见  fullLikes : 满足20点赞后可见
     */
    private String publicType = "everyone";
}
