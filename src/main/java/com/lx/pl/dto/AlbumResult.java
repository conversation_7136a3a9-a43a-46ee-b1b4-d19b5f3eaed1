package com.lx.pl.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AlbumResult {

    @Schema(description = "图片id")
    private Long albumImgId;

    @Schema(description = "用户名称")
    private String loginName;

    @Schema(description = "文件名称")
    private String imgName;

    @Schema(description = "缩略图名称")
    private String thumbImgName;

    @Schema(description = "文件路径")
    private String imgUrl;

    @Schema(description = "缩略图路径")
    private String thumbImgUrl;

    @Schema(description = "算出的宽")
    private Integer width;

    @Schema(description = "算出的高")
    private Integer height;

    @Schema(description = "图片大小")
    private Long size;

    @Schema(description = "真实宽")
    private Integer realWidth;

    @Schema(description = "真实高")
    private Integer realHeight;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
