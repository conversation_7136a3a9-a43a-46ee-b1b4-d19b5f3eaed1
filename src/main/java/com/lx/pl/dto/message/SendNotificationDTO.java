package com.lx.pl.dto.message;


import com.lx.pl.enums.PushTypeEnum;
import com.lx.pl.enums.SchemeEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
@Builder
public final class SendNotificationDTO {

    private List<String> targetTokenList;

    private PushTypeEnum pushType;

    private String title;

    private String body;

    private SchemeEnum schemeEnum;

    private Long messageId;

    private String imageUrl;

    private String thumbnailUrl;


}
