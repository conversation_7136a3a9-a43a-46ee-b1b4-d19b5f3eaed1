package com.lx.pl.dto.message;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

@Getter
@ToString
@Setter
public class SendNotificationCountDTO {

    private long startTime = System.currentTimeMillis();


    private final AtomicLong successCount = new AtomicLong(0);
    private final AtomicLong failureCount = new AtomicLong(0);
    private final AtomicLong limitCount = new AtomicLong(0);

    public void incrementSuccessCount() {
        successCount.incrementAndGet();
    }

    public void incrementFailureCount() {
        failureCount.incrementAndGet();
    }

    public void incrementLimitCount() {
        limitCount.incrementAndGet();
    }

    public void addSuccessCount(int delta) {
        successCount.addAndGet(delta);
    }

    public void addFailureCount(int delta) {
        failureCount.addAndGet(delta);
    }

    public void addLimitCount(int delta) {
        limitCount.addAndGet(delta);
    }

    public Long getSuccessCount() {
        return successCount.get();
    }

    public Long getFailureCount() {
        return failureCount.get();
    }

    public Long getLimitCount() {
        return limitCount.get();
    }

    public long getTimeElapsed() {
        return System.currentTimeMillis() - startTime;
    }
}
