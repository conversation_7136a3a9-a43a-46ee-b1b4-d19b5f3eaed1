package com.lx.pl.dto;

import java.util.List;

public class ForgeParameters {

    private List<ForgeFiles> forgeFiles;

    public List<ForgeFiles> getForgeFiles() {
        return forgeFiles;
    }

    public void setForgeFiles(List<ForgeFiles> forgeFiles) {
        this.forgeFiles = forgeFiles;
    }

    public static class ForgeFiles {

        private String filename;
        private String subfolder;
        private String type;

        public String getFilename() {
            return filename;
        }

        public void setFilename(String filename) {
            this.filename = filename;
        }

        public String getSubfolder() {
            return subfolder;
        }

        public void setSubfolder(String subfolder) {
            this.subfolder = subfolder;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

    }
}
