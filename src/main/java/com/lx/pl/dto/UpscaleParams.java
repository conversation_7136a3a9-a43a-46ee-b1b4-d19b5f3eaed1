package com.lx.pl.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class UpscaleParams {

    @Schema(description = "upscale类型: upscale_normal  或者upscale_anime(用于动漫)")
    private String type;

    @Schema(description = "高清修复倍数")
    private double scale_by;

    @Schema(description = "图片地址")
    private String img_url;

    @Schema(description = "标记id")
    private String mark_id;

    @Schema(description = "服务器地址id")
    private String address;

    @Schema(description = "是否开启模型加速，默认为：false")
    private Boolean accelerated;
}
