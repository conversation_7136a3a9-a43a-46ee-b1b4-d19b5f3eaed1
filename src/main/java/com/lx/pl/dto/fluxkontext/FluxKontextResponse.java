package com.lx.pl.dto.fluxkontext;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Flux Kontext Pro API响应
 *
 * <AUTHOR>
 */
public class FluxKontextResponse {

    /**
     * 基础响应数据
     */
    @Data
    public static class BaseResponseData {
        /**
         * 请求ID
         */
        private String id;

        /**
         * 轮询URL
         */
        @JsonProperty("polling_url")
        private String pollingUrl;
    }

    /**
     * 任务结果响应
     */
    @Data
    public static class TaskResultResponse {
        /**
         * 任务ID
         */
        private String id;

        /**
         * 任务状态 (Queued, Processing, Ready, Error)
         */
        private String status;

        /**
         * 结果数据
         */
        private ResultData result;

        /**
         * 错误信息
         */
        private String error;
    }

    /**
     * 结果数据
     */
    @Data
    public static class ResultData {
        /**
         * 生成的图像URL
         */
        private String sample;

        /**
         * 图像宽度
         */
        private Integer width;

        /**
         * 图像高度
         */
        private Integer height;

        /**
         * 使用的种子值
         */
        private Integer seed;

        /**
         * 处理时间（秒）
         */
        @JsonProperty("processing_time")
        private Double processingTime;
    }

    /**
     * 错误响应
     */
    @Data
    public static class ErrorResponse {
        /**
         * 错误代码
         */
        private String code;

        /**
         * 错误消息
         */
        private String message;

        /**
         * 详细信息
         */
        private String detail;
    }
}
