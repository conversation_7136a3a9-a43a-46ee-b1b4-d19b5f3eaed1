package com.lx.pl.dto.fluxkontext;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Flux Kontext Pro API请求参数
 *
 * <AUTHOR>
 */
public class FluxKontextRequest {

    /**
     * 文本生图请求
     */
    @Data
    public static class TextToImageRequest {
        /**
         * 文本描述
         */
        private String prompt;

        /**
         * 宽高比 (例如: "16:9", "1:1")
         */
        @JsonProperty("aspect_ratio")
        private String aspectRatio;

        /**
         * 随机种子
         */
        private Integer seed;

        /**
         * 是否启用prompt增强
         */
        @JsonProperty("prompt_upsampling")
        private Boolean promptUpsampling = false;

        /**
         * 安全容忍度 (0-6)
         */
        @JsonProperty("safety_tolerance")
        private Integer safetyTolerance = 2;

        /**
         * 输出格式
         */
        @JsonProperty("output_format")
        private String outputFormat = "jpeg";

        /**
         * Webhook URL
         */
        @JsonProperty("webhook_url")
        private String webhookUrl;

        /**
         * Webhook密钥
         */
        @JsonProperty("webhook_secret")
        private String webhookSecret;
    }

    /**
     * 图像编辑请求
     */
    @Data
    public static class ImageEditRequest {
        /**
         * 编辑指令
         */
        private String prompt;

        /**
         * Base64编码的输入图像
         */
        @JsonProperty("input_image")
        private String inputImage;

        /**
         * 宽高比
         */
        @JsonProperty("aspect_ratio")
        private String aspectRatio;

        /**
         * 随机种子
         */
        private Integer seed;

        /**
         * 是否启用prompt增强
         */
        @JsonProperty("prompt_upsampling")
        private Boolean promptUpsampling = false;

        /**
         * 安全容忍度
         */
        @JsonProperty("safety_tolerance")
        private Integer safetyTolerance = 2;

        /**
         * 输出格式
         */
        @JsonProperty("output_format")
        private String outputFormat = "jpeg";

        /**
         * Webhook URL
         */
        @JsonProperty("webhook_url")
        private String webhookUrl;

        /**
         * Webhook密钥
         */
        @JsonProperty("webhook_secret")
        private String webhookSecret;
    }
}
