package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class RandomPageInfo<T> {

    @Schema(description = "分页数据集合")
    List<T> resultList;

    @Schema(description = "第几页")
    Integer pageNum;

    @Schema(description = "每页记录数")
    Integer pageSize;

    @Schema(description = "总数")
    Integer total;

    @Schema(description = "分页页数随机集合")
    List<Integer> randomPageList;
}
