package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "lumen任务标准")
public class TaskLumenStandards {

    @Schema(description = "任务id")
    private  String taskId;

    @Schema(description = "任务名称")
    private  String taskName;

    @Schema(description = "任务目标次数")
    private  Integer targetCount;

    @Schema(description = "完成后奖励的 lumen 币")
    private  Integer reward;

    @Schema(description = "任务描述")
    private  String description;

    /**
     * 任务组id
     * {@link com.lx.pl.enums.TaskGroup}
     */
    @Schema(description = "任务组id")
    private Integer taskGroupId;

    @Schema(description = "组内任务排序")
    private Integer order;
}
