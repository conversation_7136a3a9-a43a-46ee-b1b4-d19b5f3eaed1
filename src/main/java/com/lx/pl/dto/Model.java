package com.lx.pl.dto;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
public class Model {

    private List<ModelDetail> models;

    @Data
    public static class ModelDetail {

        private String id;
        private String name;
        private String des;
        private List<SupportedSize> supportedSizes;
        private DefaultParas defaultParas;
        private Map<String, Object> model_ability;
        private String modelAvatar;

    }

    @Data
    @AllArgsConstructor
    public static class DefaultParas {

        private int seed;
        private int steps;
        private double cfg;
        private String sampler_name;
        private String scheduler;
        private double denoise;
        private String positive_prompt;
        private String negative_prompt;
        private double hires_fix_denoise;
        private double hires_scale;
    }

    @Data
    @AllArgsConstructor
    public static class SupportedSize {

        private int width;
        private int height;
    }
}
