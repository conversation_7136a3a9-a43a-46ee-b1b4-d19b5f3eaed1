package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * @Description: 批量rmbg参数类
 * @Author: senlin_he
 * @Date: 2025/5/13
 */
@Data
public class ToolRmbgParam implements Serializable {

    @Schema(description = "OSS文件url")
    private String fileUrl;

    @Schema(description = "图片宽")
    private Integer width;

    @Schema(description = "图片高")
    private Integer height;

    @Schema(description = "batchId")
    private String batchId;

}
