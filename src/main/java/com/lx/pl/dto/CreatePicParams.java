package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CreatePicParams {

    @Schema(description = "是否快速生图模式")
    private Boolean fastHour;

    @Schema(description = "平台信息")
    private String platform;

    @Schema(description = "任务标记id")
    private String markId;

    @Schema(description = "relax任务需要等待的时间")
    private Integer relaxWaitTime;

    @Schema(description = "是否超200万像素生图")
    private Boolean highPixels;
}
