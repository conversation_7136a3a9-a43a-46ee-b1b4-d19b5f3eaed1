package com.lx.pl.dto;

import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

@ToString
@Data
public class HomeFileResult {

    @Schema(description = "用户名称")
    private String userName;

    @Schema(description = "用户名称")
    private String loginName;

    @Schema(description = "头像信息")
    private String avatar;

    @Schema(description = "文件名称")
    private String imgName;

    @Schema(description = "缩略图名称")
    private String thumbnailName;

    @Schema(description = "高清缩略图名称")
    private String highThumbnailName;

    @Schema(description = "文件路径")
    private String imgUrl;

    @Schema(description = "缩略图路径")
    private String thumbnailUrl;

    @Schema(description = "高清缩略图路径")
    private String highThumbnailUrl;

    private String promptId;

    @Schema(description = "创建日期")
    private LocalDateTime createTimestamp;

    @Schema(description = "点赞数")
    private int likeNums;

    @Schema(description = "当前用户是否已经点赞")
    private Boolean hasLike;

    @Schema(description = "生图信息")
    private GenGenericPara genInfo;

    @Schema(description = "生成图片的宽")
    private int realWidth;

    @Schema(description = "生成图片的高")
    private int realHeight;

    @Schema(description = "是否是竞赛专属")
    private Boolean competition;

    @Schema(description = "生图原始操作")
    private String originCreate;
}
