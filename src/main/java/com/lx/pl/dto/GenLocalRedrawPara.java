package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@ToString
@Data
public class GenLocalRedrawPara {

    @Schema(description = "图片url")
    private String img_url;

    @Schema(description = "局部重绘过的图片url(黑白)")
    private String mask_img_url;

    @Schema(description = "描绘过的图片url")
    private String draw_img_url;

    @Schema(description = "去噪指数")
    private double denoise;
}
