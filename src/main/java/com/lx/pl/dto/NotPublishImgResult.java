package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class NotPublishImgResult {

    /**
     * 用户名称
     */
    private String loginName;

    private String promptId;

    /**
     * 文件名称
     */
    private String imgName;

    /**
     * 生成图片的宽
     */
    private int realWidth;

    /**
     * 生成图片的高
     */
    private int realHeight;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    @Schema(description = "是否公开(0:未公开 1:已公开 2:审核中  3.已拒绝)")
    private Integer isPublic;

    /**
     * 30% 高清图
     */
    @Schema(description = "30% 高清图 (默认30% 高清图 老数据可能30% 高清图 若没有则展示(thumbnailUrl 或者imgUrl 优先级为(highMiniUrl -- thumbnailUrl -- imgUrl)))")
    private String highMiniUrl;

    /**
     * 文件路径
     */
    @Schema(description = "文件路径(原图)")
    private String imgUrl;

    /**
     * 缩略图路径
     */
    @Schema(description = "缩略图路径")
    private String thumbnailUrl;

}
