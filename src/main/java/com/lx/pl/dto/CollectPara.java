package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@ToString
@Data
public class CollectPara {

    @Schema(description = "生图promptId")
    private String promptId;

    @Schema(description = "文件名称")
    private String imgName;

    @Schema(description = "缩略图名称")
    private String thumbnailName;

    @Schema(description = "文件路径")
    private String imgUrl;

    @Schema(description = "缩略图路径")
    private String thumbnailUrl;

    @Schema(description = "正向提示词")
    private String prompt;

    @Schema(description = "反向提示词")
    private String negativePrompt;

    @Schema(description = "文件夹id")
    private String classifyId;

    @Schema(description = "高清缩略图名称")
    private String highThumbnailName;

    @Schema(description = "高清缩略图路径")
    private String highThumbnailUrl;
}
