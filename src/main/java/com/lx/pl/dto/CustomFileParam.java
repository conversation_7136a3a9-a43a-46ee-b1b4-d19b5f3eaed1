package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;


/**
 * 用户上传参数
 *
 * <AUTHOR>
 */
@Data
public class CustomFileParam implements Serializable {

    //    @Schema(description = "OSS文件名称")
//    private String fileName;

    @Schema(description = "OSS文件url")
    private String fileUrl;

    // @Schema(description = "高清url")
    // private String thumbnailUrl;
    //
    // @Schema(description = "超清url")
    // private String highThumbnailUrl;

    @Schema(description = "图片宽")
    private Integer width;

    @Schema(description = "图片高")
    private Integer height;

    @Schema(description = "是否是piclumen生产")
    private boolean bePiclumen;

    @Schema(description = "piclumen生图参数")
    private GenGenericPara genGenericPara;

    @Schema(description = "file来源类型: crop : 裁剪")
    private String originCreate;

//    @Schema(description = "后缀名称")
//    private String fileExt;
}
