package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

@Data
public class TranslateParams {

    @Schema(description = "翻译的内容")
    private String content;

    @Schema(description = "目标语言编码")
    private String languageType;

    @Schema(description = "处理类型 translation|enhance")
    private String mode;
}
