package com.lx.pl.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@ToString
@Data
public class EnlargeImagePara {

    @Schema(description = "图片路径")
    private String img_url;

    @Schema(description = "描绘过的图片url")
    private String draw_img_url;

    @Schema(description = "向左扩图px")
    private int left;

    @Schema(description = "向顶部图px")
    private int top;

    @Schema(description = "向由扩图px")
    private int right;

    @Schema(description = "向下扩图px")
    private int bottom;

}
