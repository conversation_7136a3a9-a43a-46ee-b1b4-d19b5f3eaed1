package com.lx.pl.dto.mq;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description: 就绪服务信息
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ReadyServerVo {
    /**
     * 服务器id
     */
    private String serverId;

    private String promptId;

    /**
     * 服务器地址
     */
    private String hostAndPort;

    /**
     * 就绪状态
     */
    private Boolean ready;

    /**
     * 时间戳
     */
    private Long timestamp;
}
