package com.lx.pl.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class EnlargeImageParams {

    @Schema(description = "模型id")
    private String model_id;

    @Schema(description = "正向提示词")
    private String prompt;

    @Schema(description = "图片路径")
    private String img_url;

    @Schema(description = "描绘过的图片url")
    private String draw_img_url;

    @Schema(description = "向左扩图px")
    private int left;

    @Schema(description = "向顶部图px")
    private int top;

    @Schema(description = "向由扩图px")
    private int right;

    @Schema(description = "向下扩图px")
    private int bottom;

    @Schema(description = "标记id")
    private String mark_id;

    @Schema(description = "生图回调接口")
    private String callback_url;

    @Schema(description = "服务器地址id")
    private String address;

    @Schema(description = "是否开启模型加速，默认为：false")
    private Boolean accelerated;
}
