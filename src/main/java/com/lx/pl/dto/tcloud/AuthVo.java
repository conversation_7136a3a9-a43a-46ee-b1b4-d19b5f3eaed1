package com.lx.pl.dto.tcloud;

import com.tencent.cloud.Response;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class AuthVo implements Serializable {

    private static final long serialVersionUID = 3530402901153822373L;
    private Response response;

    private String bucket;

    private String region;

    private String fileName;

    private String fullPath;

    // private String thumbnailName;
    //
    // private String thumbnailUrl;
    //
    // private String thumbnailRule;
    //
    // private String highThumbnailRule;
    //
    // private String highThumbnailName;
    //
    // private String highThumbnailUrl;

    private boolean success;

    public AuthVo() {
    }

    public AuthVo(boolean success) {
        this.success = success;
    }
}
