package com.lx.pl.dto.tcloud;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AuthParam {
    @Schema(description = "文件后缀")
    @NotEmpty(message = "file ext cant be empty")
    private String fileExt;

    @Schema(description = "上传类型 normal/suggest/temp")
    private String type;

    @Schema(description = "batchId")
    private String batchId;
}
