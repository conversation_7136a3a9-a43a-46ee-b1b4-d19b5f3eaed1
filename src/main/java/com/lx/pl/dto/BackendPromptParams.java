package com.lx.pl.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;


@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BackendPromptParams {

    @Schema(description = "正向提示词")
    private String prompt;

    @Schema(description = "模型ID")
    private String model_id;

    @Schema(description = "种子数，10位整数")
    private Long seed;

    @Schema(description = "反向提示词")
    private String negative_prompt;

    @Schema(description = "采样器，默认选euler_ancestral，可选项：[\"euler\", \"euler_ancestral\", \"heun\", \"heunpp2\",\"dpm_2\", \"dpm_2_ancestral\",\"lms\", \"dpm_fast\", \"dpm_adaptive\", \"dpmpp_2s_ancestral\", \"dpmpp_sde\", \"dpmpp_sde_gpu\", \"dpmpp_2m\", \"dpmpp_2m_sde\", \"dpmpp_2m_sde_gpu\", \"dpmpp_3m_sde\", \"dpmpp_3m_sde_gpu\", \"ddpm\", \"lcm\"]")
    private String sampler;

    @Schema(description = "调度器，默认选karras，可选项：[\"normal\", \"karras\", \"exponential\", \"sgm_uniform\", \"simple\", \"ddim_uniform\"]")
    private String scheduler;

    @Schema(description = "迭代步数，默认20步，1-60步之间")
    private Integer steps;

    @Schema(description = "提示词引导系数，默认5.0，1.0-30.0之间")
    private Double cfg;

    @Schema(description = "降噪幅度，0.00-1.00之间")
    private double denoise;

    @Schema(description = "批量生成数")
    private int batch_size;

    @Schema(description = "宽")
    private int width;

    @Schema(description = "高")
    private int height;

    @Schema(description = "速度  fast:快 , normal:一般 , slow:慢")
    private String speed = "normal";

    @Schema(description = "优先级：1-10")
    private int priority = -1;

    @Schema(description = "超清修复")
    private Extra_data extra_data;

    @Schema(description = "生图回调接口")
    private String callback_url;

    @Schema(description = "标记id")
    private String mark_id;

    @Schema(description = "生图模式:(fast ： 快速生图   quality ： 高质量生图)")
    private String gen_mode;

    @Schema(description = "图生图参数")
    private Img2img_info img2img_info;

    @Schema(description = "服务器地址id")
    private String address;

    @Schema(description = "是否开启模型加速，默认为：false")
    private Boolean accelerated;

    @ToString
    @Data
    public static class Extra_data {

        @Schema(description = "0.0 - 1.0 之间，默认 0 不生效")
        private double anime_style;

        @Schema(description = "优先级：1-10")
        private Hires_fix hires_fix;
    }

    @ToString
    @Data
    public static class Hires_fix {

        @Schema(description = "是否超清修复开关： 0 ： 关闭  1 ： 开启")
        private int status;

        @Schema(description = "超清修复倍数")
        private double scale;
    }

    @ToString
    @Data
    public static class Img2img_info {
        List<GenGenericPara.StyleImg> style_list;
    }

    @ToString
    @Data
    public static class StyleImg {
        @Schema(description = "图片url")
        private String img_url;

        @Schema(description = "图片风格")
        private String style;

        @Schema(description = "图生图权重")
        private Double weight;
    }
}
