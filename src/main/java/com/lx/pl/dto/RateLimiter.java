package com.lx.pl.dto;

import java.util.concurrent.atomic.AtomicInteger;

public class RateLimiter {

    private final int limit;
    private final long timeout;
    private AtomicInteger requestCount;
    private long lastResetTime;

    public RateLimiter(int limit, long timeout) {
        this.limit = limit;
        this.timeout = timeout;
        this.requestCount = new AtomicInteger(0);
        this.lastResetTime = System.currentTimeMillis();
    }

    public synchronized boolean tryAcquire() {
        long currentTime = System.currentTimeMillis();

        if (currentTime - lastResetTime > timeout) {
            requestCount.set(0);
            lastResetTime = currentTime;
        }

        if (requestCount.incrementAndGet() > limit) {
            return false;
        }

        return true;
    }
}
