package com.lx.pl.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HiresFixParams {

    @Schema(description = "高清修复倍数")
    private double scale_by;

    @Schema(description = "图片地址")
    private String img_url;

    @Schema(description = "回调地址")
    private String callback_url;

    private String prompt_id;

    @Schema(description = "标记id")
    private String mark_id;

    @Schema(description = "去噪指数")
    private double hd_denoise;

    @Schema(description = "服务器地址id")
    private String address;

    @Schema(description = "是否开启模型加速，默认为：false")
    private Boolean accelerated;

    @Schema(description = "正向提示词")
    private String prompt;

    @Schema(description = "种子数，10位整数")
    private Long seed;

    @Schema(description = "反向提示词")
    private String negative_prompt;

    @Schema(description = "采样器，默认选euler_ancestral，可选项：[\"euler\", \"euler_ancestral\", \"heun\", \"heunpp2\",\"dpm_2\", \"dpm_2_ancestral\",\"lms\", \"dpm_fast\", \"dpm_adaptive\", \"dpmpp_2s_ancestral\", \"dpmpp_sde\", \"dpmpp_sde_gpu\", \"dpmpp_2m\", \"dpmpp_2m_sde\", \"dpmpp_2m_sde_gpu\", \"dpmpp_3m_sde\", \"dpmpp_3m_sde_gpu\", \"ddpm\", \"lcm\"]")
    private String sampler;

    @Schema(description = "调度器，默认选karras，可选项：[\"normal\", \"karras\", \"exponential\", \"sgm_uniform\", \"simple\", \"ddim_uniform\"]")
    private String scheduler;

    @Schema(description = "迭代步数，默认20步，1-60步之间")
    private Integer steps;

    @Schema(description = "提示词引导系数，默认5.0，1.0-30.0之间")
    private Double cfg;

    @Schema(description = "降噪幅度，0.00-1.00之间")
    private double denoise;

    @Schema(description = "宽")
    private int width;

    @Schema(description = "高")
    private int height;

    @Schema(description = "模型ID")
    private String model_id;

}
