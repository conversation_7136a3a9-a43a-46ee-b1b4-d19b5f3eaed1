package com.lx.pl.dto;


import javax.validation.constraints.NotEmpty;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;

@Data
@Schema(description = "apple授权登录DTO")
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppleLoginDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "apple授权登录码", required = true)
    @NotEmpty(message = "apple授权登录码不能为空")
    private String identityToken;

    @Schema(description = "apple用户名称")
    private String userName;
}

