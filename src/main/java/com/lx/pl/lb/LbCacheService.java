package com.lx.pl.lb;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.service.RedisService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Service
public class LbCacheService {

    @Resource
    RedisService redisService;

    private final Cache<String, Integer> cache = CacheBuilder.newBuilder()
            .expireAfterWrite(3, TimeUnit.MINUTES)
            .build();

    public Integer getFromCacheOrRedis(String key, Integer defaultValue) {
        // 先从缓存中获取
        Integer value = cache.getIfPresent(key);

        if (value == null) {
            // 如果缓存中没有，则从 Redis 获取
            value = (Integer) redisService.getDataFromHash(LogicConstants.LB_SYSTEM_PARA, key);

            if (value == null) {
                // 如果 Redis 中也没有对应的值，则使用默认值
                value = defaultValue;
            }

            // 将获取到的值（包括默认值）放入缓存
            cache.put(key, value);
        }
        return value;
    }
}
