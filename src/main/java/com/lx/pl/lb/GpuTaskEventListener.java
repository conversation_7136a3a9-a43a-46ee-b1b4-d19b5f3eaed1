package com.lx.pl.lb;

import com.lx.pl.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
public class GpuTaskEventListener {

    @Autowired
    private RedisService redisService;


    @EventListener
    public void handleTaskCompletedEvent(TaskCompletedEvent event) {
        String serverId = event.getServerId();
        //用预载队列方式管理列表
        String instance = (String) redisService.rightPop(serverId);
        String message = "The server with ID: " + serverId + " has completed the task with instanceID:" + instance + ".";
        System.out.println(message);
        //目前因为定时器速度较快，暂不考虑使用主动加载任务
    }
}
