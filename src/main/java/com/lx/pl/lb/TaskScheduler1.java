package com.lx.pl.lb;

import com.lx.pl.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class TaskScheduler1 {

    @Autowired
    private GpuService gpuService;

    @Autowired
    private RedisService redisService;

    private static final int FAIR_TASK_THRESHOLD = 7;   // 公平队列任务阈值
    private static final int UNFAIR_TASK_THRESHOLD = 3; // 非公平队列任务阈值

    private int fairTaskCounter = 0;   // 公平队列任务计数器
    private int unfairTaskCounter = 0; // 非公平队列任务计数器

    private boolean processingFairQueue = true;  // 当前是否处理公平队列

    //    @Scheduled(fixedDelay = 5000)  // 每5秒检查一次
    public void processTasks() {
//        ConcurrentHashMap<String, Boolean> gpuServerStatus = taskManager.getGpuServerStatus();
//        String fairQueueName = "fairQueue_m1_g1";
//        String unfairQueueName = "unfairQueue_m1_g1";
//
//        for (String serverId : gpuServerStatus.keySet()) {
//            if (gpuServerStatus.get(serverId)) { // 如果服务器是空闲的
//                if (processingFairQueue) {
//                    if (fairTaskCounter < FAIR_TASK_THRESHOLD) {
//                        // 异步获取公平队列任务
//                        CompletableFuture<Task> fairTaskFuture = getTaskAsync(fairQueueName);
//
//                        // 任务获取完成后的处理逻辑
//                        fairTaskFuture.thenAccept(task -> {
//                            if (task != null) {
//                                fairTaskCounter++;
//                                assignTaskToGpuServer(serverId, task);
//                            } else {
//                                // 如果公平队列空了，尝试处理非公平队列
//                                processingFairQueue = false;
//                                fairTaskCounter = 0;
//                                processTasks(); // 再次调用，继续处理非公平队列
//                            }
//                        });
//                    }
//                } else {
//                    if (unfairTaskCounter < UNFAIR_TASK_THRESHOLD) {
//                        // 异步获取非公平队列任务
//                        CompletableFuture<Task> unfairTaskFuture = getTaskAsync(unfairQueueName);
//
//                        // 任务获取完成后的处理逻辑
//                        unfairTaskFuture.thenAccept(task -> {
//                            if (task != null) {
//                                unfairTaskCounter++;
//                                assignTaskToGpuServer(serverId, task);
//                            } else {
//                                // 如果非公平队列空了，尝试处理公平队列
//                                processingFairQueue = true;
//                                unfairTaskCounter = 0;
//                                processTasks(); // 再次调用，继续处理公平队列
//                            }
//                        });
//                    }
//                }
//
//                // 如果公平和非公平任务都处理完阈值任务，则重置计数器
//                if (fairTaskCounter >= FAIR_TASK_THRESHOLD && unfairTaskCounter >= UNFAIR_TASK_THRESHOLD) {
//                    fairTaskCounter = 0;
//                    unfairTaskCounter = 0;
//                }
//            }
//        }
    }

//    // 分配任务到GPU服务器
//    private void assignTaskToGpuServer(String serverId, Task task) {
//        gpuService.processTask(serverId, task);
////        taskManager.updateGpuServerStatus(serverId, false);  // 更新服务器状态为忙碌
//    }
//
//    // 异步获取任务的方法
//    @Async
//    public CompletableFuture<Task> getTaskAsync(String queueName) {
//         task = redisService.pollTask(queueName); // 非阻塞获取
//        return CompletableFuture.completedFuture(task);
//    }
}
