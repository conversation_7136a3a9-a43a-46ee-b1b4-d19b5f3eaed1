package com.lx.pl.lb;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lx.pl.service.LoadBalanceService;
import com.lx.pl.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Service
public class GpuService {

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Autowired
    private RedisService redisService;

    @Resource
    private LoadBalanceService loadBalanceService;

    // 模拟处理任务的方法
    @Async("gpuTaskExecutor")
    public void processTask(String serverId, String markId, String queueName) throws JsonProcessingException {
        CompletableFuture<Map<String, Object>> taskFuture = doCreatePromAsync(serverId, markId, queueName);

        // 任务获取完成后的处理逻辑
        taskFuture.thenAccept(taskMap -> {
            //todo 保存任务信息
            // 任务完成后，发布事件通知. 这里只是demo，需要真正的生图callback之后才能有
//            eventPublisher.publishEvent(new TaskCompletedEvent(this, (String) taskMap.get("markId")));
        });
    }

    // 异步获取任务的方法
    @Async("gpuTaskExecutor")
    public CompletableFuture<Map<String, Object>> doCreatePromAsync(String serverId, String markId, String queueName) throws JsonProcessingException {
        //增加到预载队列中，等同于真实任务
        Map<String, Object> resultMap = loadBalanceService.operateBackendApi(markId, serverId, queueName);
        return CompletableFuture.completedFuture(resultMap);
    }
}
