package com.lx.pl.lb;

import cn.hutool.json.JSONUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.service.RedisService;
import com.lx.pl.util.StringUtils;
import com.lx.pl.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@Slf4j
public class DealTaskScheduler {

    @Autowired
    private GpuService gpuService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private LbCacheService lbCacheService;

    //    @Scheduled(fixedDelay = 1000)  // 每1秒检查一次
    public void processTasks2() throws JsonProcessingException {
        Integer fairTaskThreshold = (lbCacheService.getFromCacheOrRedis(LogicConstants.FAIR_TASK_THRESHOLD, 7));
        Integer unfairTaskThreshold = (lbCacheService.getFromCacheOrRedis(LogicConstants.UNFAIR_TASK_THRESHOLD, 3));
        Integer preloadingTaskThreshold = (lbCacheService.getFromCacheOrRedis(LogicConstants.PRELOADING_TASK_THRESHOLD, 1));
        Integer blobFairWaitTime = lbCacheService.getFromCacheOrRedis(LogicConstants.BLOB_FAIR_WAIT_TIME, 500);
        Map<Object, Object> ruleHash = redisService.getHashmap(LogicConstants.RULE_LIST_KEY);
        // 使用并行流处理任务
        AtomicInteger taskCounter = new AtomicInteger(0);
        ruleHash.entrySet().parallelStream().forEach(entry -> {
            taskCounter.incrementAndGet();
            String key = (String) entry.getKey();
            String serverIds = (String) entry.getValue();
            if (!LogicConstants.EXCLUDE_MG_FG.equals(key)) {
                try {
                    processTaskForKey(key, serverIds, fairTaskThreshold, unfairTaskThreshold, preloadingTaskThreshold, blobFairWaitTime);
                } catch (JsonProcessingException e) {
                    log.error("使用并行流处理任务 error", e);
                }
            }
        });

        // 最后处理 EXCLUDE_MG_FG 相关的任务
        if (ruleHash.containsKey(LogicConstants.EXCLUDE_MG_FG)) {
            String serverIds = (String) ruleHash.get(LogicConstants.EXCLUDE_MG_FG);
            processTaskForKey(LogicConstants.EXCLUDE_MG_FG, serverIds, fairTaskThreshold, unfairTaskThreshold, preloadingTaskThreshold, blobFairWaitTime);
        }

        // 处理黑名单用户
        String blRuleFluxServerIds = (String) redisService.get(LogicConstants.BL_RULE_FLUX);
        String blRuleCommonServerIds = (String) redisService.get(LogicConstants.BL_RULE_COMMON);
        if (StringUtils.isNotBlank(blRuleFluxServerIds)) {
            processTaskForBlackList(blRuleFluxServerIds, LogicConstants.BL_FLUX_QUEUE, preloadingTaskThreshold);
        }
        if (StringUtils.isNotBlank(blRuleCommonServerIds)) {
            processTaskForBlackList(blRuleCommonServerIds, LogicConstants.BL_COMMON_QUEUE, preloadingTaskThreshold);
        }
    }

    private void processTaskForKey(String key, String serverIds, Integer fairTaskThreshold, Integer unfairTaskThreshold, Integer preloadingTaskThreshold, Integer blobFairWaitTime) throws JsonProcessingException {
        String fairQueueName = LogicConstants.FAIR_QUEUE_NAME_PREFIX + key;
        String unfairQueueName = LogicConstants.UNFAIR_QUEUE_NAME_PREFIX + key;
        String fairQueueCounter = fairQueueName + LogicConstants.QUEUE_COUNTER_SUFFIX;
        String unfairQueueCounter = unfairQueueName + LogicConstants.QUEUE_COUNTER_SUFFIX;

        //服务器id不能为空
        if (StringUtil.isBlank(serverIds)) {
            return;
        }

        // 将服务器列表，进行随机打乱，随机分配
        List<String> serverIdList = Arrays.asList(serverIds.split(","));
//        Collections.shuffle(serverIdList, new Random());

        for (String serverId : serverIdList) {
            boolean canProcess = redisService.comfySizeCheck(LogicConstants.COMFY_SIZE_PREFIX + serverId, preloadingTaskThreshold, fairQueueName, unfairQueueName);
            if (canProcess) {
                String markId = null;
                String queueName = null;

                // 原子性地增加公平队列的计数器
                long fairTaskCounter = redisService.incrementTaskCounter(fairQueueCounter);

                long unfairTaskCounter = 0;
                if (fairTaskCounter <= fairTaskThreshold) {
                    // 尝试从公平队列中获取任务
//                            markId = (String) redisService.pollTaskBlob(fairQueueName, blobFairWaitTime, TimeUnit.MILLISECONDS);
//                          System.out.println("队列："+redisTemplate.opsForList().range(fairQueueName,0,-1));
                    markId = (String) redisService.rightPop(fairQueueName);

                    if (StringUtils.isBlank(markId)) {
                        // 如果没有任务，减少计数器
                        redisService.decrementTaskCounter(fairQueueCounter);

                        // 尝试处理非公平队列
                        markId = (String) redisService.rightPop(unfairQueueName);
                        if (StringUtils.isNotBlank(markId)) {
                            redisService.incrementTaskCounter(unfairQueueCounter);
                            queueName = unfairQueueName;
                        }
                    } else {
                        queueName = fairQueueName;
                    }
                } else {
                    // 由于进入方法时就新增了公平数量，需要重新把计数器值降下来
                    redisService.decrementTaskCounter(fairQueueCounter);

                    // 原子性地增加非公平队列的计数器
                    unfairTaskCounter = redisService.incrementTaskCounter(unfairQueueCounter);

                    if (unfairTaskCounter <= unfairTaskThreshold) {
                        markId = (String) redisService.rightPop(unfairQueueName);
                        if (StringUtils.isBlank(markId)) {
                            // 如果没有任务，减少计数器
                            redisService.decrementTaskCounter(unfairQueueCounter);
                            // 如果非公平队列空了，检查是否可以处理公平队列
                            markId = (String) redisService.rightPop(fairQueueName);
                            if (StringUtils.isNotBlank(markId)) {
                                //说明非公平队列为空，而公平队列有排队，直接重置公平队列和非公平，直接优先处理公平队列
                                redisService.resetTaskCounter(fairQueueCounter);
                                redisService.resetTaskCounter(unfairQueueCounter);
                                queueName = fairQueueName;
                            }
                        } else {
                            queueName = unfairQueueName;
                        }
                    } else {

                        // 如果超过非公平数量，需要重新把计数器值降下来
                        redisService.decrementTaskCounter(unfairQueueCounter);
                        // 如果非公平队列空了，检查是否可以处理公平队列
                        markId = (String) redisService.rightPop(fairQueueName);

                        if (StringUtils.isNotBlank(markId)) {
                            //说明非公平队列为空，而公平队列有排队，直接重置公平队列和非公平，直接优先处理公平队列
                            redisService.resetTaskCounter(fairQueueCounter);
                            redisService.resetTaskCounter(unfairQueueCounter);
                            queueName = fairQueueName;
                        }
                    }
                }

                // 如果所有队列都为空，或者计数器满了，重置计数器 。
                // todo 如果多线程这里会有问题
                if (fairTaskCounter >= fairTaskThreshold && unfairTaskCounter >= unfairTaskThreshold) {
                    redisService.resetTaskCounter(fairQueueCounter);
                    redisService.resetTaskCounter(unfairQueueCounter);
                }
                // 分配任务给GPU服务器
                if (StringUtils.isNotBlank(markId)) {
                    // 更新服务器状态改为预加载阈值方法，这个方法中就需要增加serverId对应的list列表
                    //用预载队列方式管理列表
                    redisService.leftPush(serverId, markId, 3, TimeUnit.MINUTES);
                    //设置过期时间防止长时间不变化导致的一直卡死,有新任务会续期，没新任务直接3分钟过期
                    redisService.expire(LogicConstants.COMFY_SIZE_PREFIX + serverId, 3, TimeUnit.MINUTES);
                    redisService.putDataToHash(LogicConstants.MARKID_SERVERID_LIST, markId, serverId, 1, TimeUnit.HOURS);
                    gpuService.processTask(serverId, markId, queueName);
                } else {
                    redisService.comfySizeDecr(LogicConstants.COMFY_SIZE_PREFIX + serverId);
                }
            }
        }
    }

    private void processTaskForBlackList(String serverIds, String queueName, Integer preloadingTaskThreshold) throws JsonProcessingException {
        for (String serverId : serverIds.split(",")) {
            if (redisService.listSize(serverId) <= preloadingTaskThreshold) {
                String markId = (String) redisService.rightPop(queueName);
                // 分配任务给GPU服务器
                if (StringUtils.isNotBlank(markId)) {
                    //用预载队列方式管理列表
                    redisService.leftPush(serverId, markId, 3, TimeUnit.MINUTES);
                    //设置过期时间防止长时间不变化导致的一直卡死
                    redisService.expire(LogicConstants.COMFY_SIZE_PREFIX + serverId, 3, TimeUnit.MINUTES);
                    redisService.putDataToHash(LogicConstants.MARKID_SERVERID_LIST, markId, serverId, 1, TimeUnit.HOURS);
                    gpuService.processTask(serverId, markId, queueName);
                }
            }
        }
    }
}
