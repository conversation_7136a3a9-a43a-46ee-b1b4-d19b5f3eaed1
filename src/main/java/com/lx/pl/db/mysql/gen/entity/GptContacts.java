package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 联系我们对象 gpt_contacts
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("gpt_contacts")
public class GptContacts extends MyBaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 用户账号
     */
    private String loginName;

    /**
     * 联系名称
     */
    @NotNull
    @Size(min = 1, max = 100, message = "Number of items must be between 1 and 100")
    private String fullName;

    /**
     * 原因 1-6 为业务相关 7 为支付相关
     */
    private Integer reason;

    /**
     * 内容
     */
    @NotEmpty
    @Size(min = 1, max = 3000, message = "Number of items must be between 1 and 3000")
    private String message;

    /**
     * 联系邮箱
     */
    @NotNull
    @Email(message = "Invalid email address")
    private String email;


    /**
     * 压缩图路径集(逗号分隔)
     */
    private String thumbnailUrls;

    @TableField(exist = false)
    @Schema(description = "压缩图片地址列表(前端传参)")
    private List<String> urlList;


    private String userAgent;
}