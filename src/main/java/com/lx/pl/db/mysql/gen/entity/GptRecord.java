package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("gpt_prompt_record_test")
public class GptRecord extends MyBaseEntity {

    @TableId(value = "id")
    private Long id;

    /**
     * 用户名称
     */
    private String loginName;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 生图信息
     */
    private JsonNode genInfo;

    /**
     * 生图模式:(fast ： 快速生图   quality ： 高质量生图)
     */
    private String genMode;

    private String promptId;

    /**
     * 正向提示词
     */
    private String prompt;

    /**
     * 生图信息
     */
    private JsonNode englishPrompt;

    /**
     * 反向提示词
     */
    private String negativePrompt;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 插队序号
     */
    private Integer taskNumber;


    /**
     * 标记id
     */
    private String markId;

    /**
     * 生图原始操作
     */
    private String originCreate;

    /**
     * 发送websocket数据是否失败
     */
    private Boolean sendWsFailure;

    /**
     * 任务开始生图开始时间
     */
    private LocalDateTime genStartTime;

    /**
     * 任务结束生图时间
     */
    private LocalDateTime genEndTime;

    /**
     * 批量生成数
     */
    private int batchSize;

    /**
     * 生图比例
     */
    private String aspectRatio;

    /**
     * 模型id
     */
    private String modelId;

    /**
     * 调用py入参
     */
    private JsonNode promptParams;

    /**
     * 功能类型
     */
    private String featureName;

    /**
     * 失败信息
     */
    private String failureMessage;

    /**
     * 是否属于fastHour机制内的任务
     */
    private Boolean fastHour;

    /**
     * 是否被删除：0：未删除 1：已删除
     */
    private boolean del;

    public boolean getDel() {
        return del;
    }

    public void setDel(boolean del) {
        this.del = del;
    }
}
