package com.lx.pl.db.mysql.community.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Set;

@Data
@Document(collection = "daily_task_lumen")
public class DailyTaskLumen {

    @Id
    private String id;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "用户账号")
    private String userLoginName;

    @Schema(description = "收到的每日签到数量")
    private Integer signInNums;

    @Schema(description = "收到的每日点赞数量")
    private Integer dailyLikeNums;

    @Schema(description = "收到的每日分享数量")
    private Integer dailyShareNums;

    @Schema(description = "收到的每日评论数量")
    private Integer dailyCommentNums;

    @Schema(description = "每日免费日期")
    private Long dailyLumensTime;

    @Schema(description = "已经完成任务id集合")
    private Set<String> finishTaskIds;

    @Schema(description = "已经领取lumen任务id集合")
    private Set<String> rewardTaskIds;

    @Schema(description = "每日观看广告数量")
    private Integer dailyWatchADNums;

    @Schema(description = "上次完成观看广告任务的时间")
    private Long lastWatchADTime;
}
