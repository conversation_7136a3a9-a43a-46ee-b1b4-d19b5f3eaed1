package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("questionnaire_record")
@Schema(description = "调查问卷记录表")
public class QuestionnaireRecord extends MyBaseEntity{

    @TableId
    @Schema(description = "主键 ID")
    private Long id;

    @Schema(description = "问卷标题")
    private String title;

    @Schema(description = "问卷简介")
    private String introduction;

    @Schema(description = "详情(问卷json数据)")
    private String details;

    @Schema(description = "平台信息")
    private String platform;

    @Schema(description = "发布状态（0：未发布，1：取消发布，5：已发布）")
    private Integer publish;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "逻辑删除（0：未删除，1：已删除）")
    private Boolean del;
}
