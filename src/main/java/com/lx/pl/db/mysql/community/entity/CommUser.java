package com.lx.pl.db.mysql.community.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;


/**
 * 社区用户信息
 */
@Data
@Document(collection = "users")
public class CommUser {

    private AccountInfo accountInfo;

    /**
     * 点赞数
     */
    private Integer likeNums;

    /**
     * 关注数
     */
    private Integer followNums;

    /**
     * 粉丝数
     */
    private Integer fansNums;

    /**
     * 用户发布到社区图片数
     */
    private Integer publicImgNums;


    /**
     * 自我介绍
     */
    private String introduction;

    /**
     * 加入社区时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 自己对用户是否已经关注
     */
    @Transient
    private Boolean followed = Boolean.FALSE;

}
