package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName("gpt_user_report")
@Data
public class UserReport extends MyBaseEntity {

    /**
     * 用户ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户名称
     */
    private String loginName;

    /**
     * 任务id
     */
    private String promptId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 审核状态（0未审核 1已审核）
     */
    private Integer auditState;

    /**
     * 审核类型 1.Violence  2. Pornography 3.Racial discrimination  4 Copyright infringement  5 Other
     */
    private Integer auditType;

    /**
     * 其他内容
     */
    private String otherContent;

    /**
     * 是否下架（0不下架 1下架）
     */
    private Integer isRemoved;

}
