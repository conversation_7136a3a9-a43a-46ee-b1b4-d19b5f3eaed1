package com.lx.pl.db.mysql.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@TableName("push_message_content")
public class PushMessageContent implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 推送消息表ID
     */
    private Long pushMessageId;

    /**
     * language Code 默认 1 english
     */
    private String languageCode;

    /**
     * 默认 1 英国
     */
    private String languageName;

    /**
     * 标题
     */
    private String title;

    /**
     * 消息内容
     */
    private String body;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}
