package com.lx.pl.db.mysql.community.activity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import lombok.Data;

@Data
@TableName("comm_activity_reward_selection")
public class CommActivityRewardSelection extends MyBaseEntity {

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String loginName;

    /**
     * 社区图片ID
     */
    private String fileId;

    /**
     * 图片链接
     */
    private String fileUrl;

    /**
     * 点赞数量
     */
    private Integer likesNum;

    /**
     * 评论数量
     */
    private Integer commentNum;

    /**
     * 获得奖项
     */
    private Integer prizeLevel;

    /**
     * 奖项内容
     */
    private String rewardContent;

    /**
     * 发布评选状态：0-未发布评选，1-已发布评选
     */
    private Boolean publish;
}