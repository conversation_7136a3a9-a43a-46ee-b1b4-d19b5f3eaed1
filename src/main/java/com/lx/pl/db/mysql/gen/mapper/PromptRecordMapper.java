package com.lx.pl.db.mysql.gen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lx.pl.db.mysql.gen.entity.PromptRecord;
import com.lx.pl.dto.common.Statistics;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PromptRecordMapper extends BaseMapper<PromptRecord> {

    Long getGenCreate(@Param("startDate") String startDate, @Param("endDate") String endDate, List<String> opexLoginNameList, String genModeType);

    Double getGenPicAvgTime(@Param("startDate") String startDate, @Param("endDate") String endDate, List<String> opexLoginNameList);

    List<Statistics> getGenBatchSize(@Param("startDate") String startDate, @Param("endDate") String endDate, List<String> opexLoginNameList);

    List<Statistics> getGenOriginCreate(@Param("startDate") String startDate, @Param("endDate") String endDate, List<String> opexLoginNameList);

    Double getGenBatchSizePicAvgTime(@Param("startDate") String startDate, @Param("endDate") String endDate,
                                     @Param("batchSize") Integer batchSize, List<String> opexLoginNameList);

    Long getImgToImgStyleNums(@Param("startDate") String startDate, @Param("endDate") String endDate,
                              @Param("style") String style, List<String> opexLoginNameList);

    List<Statistics> getGenModelId(@Param("startDate") String startDate, @Param("endDate") String endDate, List<String> opexLoginNameList);

    List<Statistics> getMaxAspectRatio(@Param("startDate") String startDate, @Param("endDate") String endDate, List<String> opexLoginNameList);


    Integer updateDelFlagForUnusedPrompts(List<String> promptIds,@Param("loginName") String loginName);

    List<String> getPromptRecordEmptyImg(List<String> promptIds,@Param("loginName") String loginName);
}

