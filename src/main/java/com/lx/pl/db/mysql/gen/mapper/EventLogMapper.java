package com.lx.pl.db.mysql.gen.mapper;

import com.lx.pl.db.mysql.gen.entity.EventLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-21
 */
public interface EventLogMapper extends BaseMapper<EventLog> {

    Long getUseOperateNumsByTime(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, List<String> opexUserIdList);

    void createTable(@Param("tableName") String tableName); // 创建新表

    // 自定义方法，用于随机分页查询
    List<EventLog> selectFirstLogPerDay();

}
