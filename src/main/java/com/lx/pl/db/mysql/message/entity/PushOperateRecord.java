package com.lx.pl.db.mysql.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("push_operation_record")
public class PushOperateRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 推送消息表ID
     */
    private Long pushMessageId;

    /**
     * 打开率
     */
    private BigDecimal openRate;

    /**
     * 通过push message打开占用uv的比率
     */
    private BigDecimal uvRate;

    /**
     * 通过消息推送打开的APP， app返回
     */
    private Long openSum;

    /**
     * 当日UV
     */
    private Long uv;

    /**
     * 发送数量
     */
    private Long pushCount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}
