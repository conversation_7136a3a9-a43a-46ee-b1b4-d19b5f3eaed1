package com.lx.pl.db.mysql.community.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Data
@Document(collection = "blacklist")
public class CommBlacklist {

    @Id
    private String id;
    /**
     * 发起拉黑者用户信息
     */
    private AccountInfo ownerAcc;

    /**
     * 被拉黑者用户信息
     */
    private AccountInfo targetAcc;

    /**
     * 关注时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
