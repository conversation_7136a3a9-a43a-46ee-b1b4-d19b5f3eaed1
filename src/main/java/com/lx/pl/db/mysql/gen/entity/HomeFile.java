package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("gpt_home_file")
public class HomeFile {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 缩略图名称
     */
    private String thumbnailName;

    /**
     * 文件路径
     */
    private String fileUrl;

    /**
     * 缩略图路径
     */
    private String thumbnailUrl;


    private String promptId;

    /**
     * 正向提示词
     */
    private String prompt;

    /**
     * 反向提示词
     */
    private String negativePrompt;

    /**
     * 模型id
     */
    private String modelId;

    /**
     * 图片高度
     */
    private int height;

    /**
     * 图片宽度
     */
    private int width;

    /**
     * 动漫系数指数
     */
    private Double animeStyle;

    /**
     * 种子数，10位整数
     */
    private Long seed;

    /**
     * 迭代步数，默认20步，1-60步之间
     */
    private int steps;

    /**
     * 提示词引导系数，默认5.0，1.0-30.0之间
     */
    private double cfg;

    /**
     * 采样器，默认选euler_ancestral
     */
    private String samplerName;

    /**
     * 批量生成数
     */
    private int batchSize;

    /**
     * 调度器
     */
    private String scheduler;

    /**
     * 创建日期
     */
    private LocalDateTime createTimestamp;

    /**
     * 更新日期
     */
    private LocalDateTime updateTimestamp;

    /**
     * 是否被删除：0：未删除 1：已删除
     */
    private boolean del;

    public boolean getDel() {
        return del;
    }

    public void setDel(boolean del) {
        this.del = del;
    }
}
