package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 微信支付结果响应表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-21
 */
@TableName("gpt_wx_pay_call_back_info")
public class WxPayCallBackInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Integer id;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 微信支付系统生成的订单号
     */
    private String transactionId;

    /**
     * 交易类型 JSAPI：公众号支付 NATIVE：扫码支付 APP：APP支付 MWEB：H5支付
     */
    private String tradeType;

    /**
     * 交易状态 SUCCESS：支付成功 REFUND：转入退款 NOTPAY：未支付 CLOSED：已关闭 REVOKED：已撤销 USERPAYING：用户支付中 PAYERROR：支付失败
     */
    private String tradeState;

    /**
     * 请求报文
     */
    private String requestBody;

    /**
     * 总金额，单位为元
     */
    private Long total;

    /**
     * 用户实际支付金额，单位为元
     */
    private Long payerTotal;

    /**
     * 删除状态 0:未删除；1:已删除
     */
    private String delStatus;

    private LocalDateTime createTimestamp;

    private LocalDateTime updateTimestamp;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getTradeState() {
        return tradeState;
    }

    public void setTradeState(String tradeState) {
        this.tradeState = tradeState;
    }

    public String getRequestBody() {
        return requestBody;
    }

    public void setRequestBody(String requestBody) {
        this.requestBody = requestBody;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Long getPayerTotal() {
        return payerTotal;
    }

    public void setPayerTotal(Long payerTotal) {
        this.payerTotal = payerTotal;
    }

    public String getDelStatus() {
        return delStatus;
    }

    public void setDelStatus(String delStatus) {
        this.delStatus = delStatus;
    }

    public LocalDateTime getCreateTimestamp() {
        return createTimestamp;
    }

    public void setCreateTimestamp(LocalDateTime createTimestamp) {
        this.createTimestamp = createTimestamp;
    }

    public LocalDateTime getUpdateTimestamp() {
        return updateTimestamp;
    }

    public void setUpdateTimestamp(LocalDateTime updateTimestamp) {
        this.updateTimestamp = updateTimestamp;
    }

    @Override
    public String toString() {
        return "WxPayCallBackInfo{" +
                "id=" + id +
                ", orderId=" + orderId +
                ", transactionId=" + transactionId +
                ", tradeType=" + tradeType +
                ", tradeState=" + tradeState +
                ", requestBody=" + requestBody +
                ", total=" + total +
                ", payerTotal=" + payerTotal +
                ", delStatus=" + delStatus +
                ", createTimestamp=" + createTimestamp +
                ", updateTimestamp=" + updateTimestamp +
                "}";
    }
}
