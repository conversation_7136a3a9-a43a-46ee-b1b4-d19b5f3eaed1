package com.lx.pl.db.mysql.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName("push_message")
public class PushMessage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 推送类型
     */
    private Integer pushType;

    /**
     * 推送时间
     */
    private LocalDateTime pushTime;

    /**
     * 推送范围(对应国家)
     */
    private String pushScope;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 标题
     */
    private String title;

    /**
     * schemeUrl
     */
    private String schemeUrl;

    /**
     * 大图地址
     */
    private String picUrl;

    /**
     * 缩略图地址
     */
    private String thumbnailUrl;

    /**
     * 状态 未推送 已推送 已过期 已暂停
     * {@link com.lx.pl.enums.PushMessageStatusEnum}
     */
    private Integer status;

    /**
     * 是否每日自动推送，0-否，1-是
     */
    private Integer dailyAutoPush;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}
