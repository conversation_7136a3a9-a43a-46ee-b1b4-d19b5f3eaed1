package com.lx.pl.db.mysql.community.activity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import lombok.Data;

@Data
@TableName("comm_activity_prize_settings")
public class CommActivityPrizeSettings extends MyBaseEntity {

    /**
     * 配置ID
     */
    private Long id;

    /**
     * 奖项等级（普通排名为1-99，200人气奖等）
     */
    private Integer level;

    /**
     * 奖牌名称（如一等奖）
     */
    private String levelName;

    /**
     * 图片链接
     */
    private String icon;

    /**
     * 奖项描述
     */
    private String mark;
}