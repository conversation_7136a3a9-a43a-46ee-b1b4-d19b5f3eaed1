package com.lx.pl.db.mysql.community.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lx.pl.db.mysql.community.entity.AccountInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;

import java.time.LocalDateTime;


@Data
@ToString
public class CommFollowWithStats {
    @Id
    private String id;
    @Schema(description = "个人介绍，需要兼容null和“”的情况")
    private String introduction;
    /**
     * 发布社区时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "被点赞次数")
    private Integer likeNums;
    @Schema(description = "关注数量")
    private Integer followNums;
    @Schema(description = "粉丝数量")
    private Integer fansNums;
    @Schema(description = "社区发布图片数量")
    private Integer publicImgNums;

    @Schema(description = "用户账户信息")
    private AccountInfo userAccountInfo;

    /**
     * 自己是否已经关注过粉丝
     */
    @Transient
    @Schema(description = "自己是否已经关注过粉丝，fans列表使用")
    private Boolean followed = Boolean.FALSE;


}