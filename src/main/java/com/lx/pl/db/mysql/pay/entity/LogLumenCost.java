package com.lx.pl.db.mysql.pay.entity;

import lombok.Data;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.security.Timestamp;
import java.time.LocalDateTime;

@Data
@ToString
@Document(collection = "log_lumen_cost")
public class LogLumenCost {

    @Id
    private String id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户账号
     */
    private String loginName;

    /**
     * pay_lumen_record表关联ID
     */
    private Long payLumenRecordId;

    /**
     * 类型：1-一次性，2-VIP，3-礼物
     */
    private Integer type;

    /**
     * lumen原有数量
     */
    private Integer lumenBefore;

    /**
     * lumen花费数量
     */
    private Integer lumenCost;

    /**
     * lumen剩余数量
     */
    private Integer lumenLeft;

    /**
     * 逻辑订阅每周期结束时间
     */
    private Long logicPeriodEnd;

    /**
     * 逻辑订阅每周期开始时间
     */
    private Long logicPeriodStart;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
