package com.lx.pl.db.mysql.gen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lx.pl.db.mysql.gen.entity.UserCollect;
import com.lx.pl.dto.CollectLoginNameNum;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;
import java.util.Map;

public interface UserCollectMapper extends BaseMapper<UserCollect> {

   List<CollectLoginNameNum> selectNumByLoginNames(@Param("tableName")String tableName, @Param("loginNames") List<String> loginNames);
}
