package com.lx.pl.db.mysql.community.entity;

import lombok.Data;

@Data
public class GenInfo {
    public String model;
    public String modelId;
    public String prompt;
    public String negativePrompt;
    public Resolution resolution;
    public Object hiresFix;
    public Object rembg;
    public ModelAbility modelAbility;
    public int seed;
    public int steps;
    public double cfg;
    public String samplerName;
    public String scheduler;
    public double denoise;

    public static class Resolution {
        public int width;
        public int height;
        public int batchSize;
    }

    public static class ModelAbility {
        public double animeStyleControl;
    }
}