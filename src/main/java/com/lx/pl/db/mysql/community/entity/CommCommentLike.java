package com.lx.pl.db.mysql.community.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 社区评论点赞信息
 */
@Data
@Document(collection = "comment_likes")
public class CommCommentLike {
    @Id
    private String id;

    /**
     * 评论id
     */
//    @Indexed
    private String commentId;

    /**
     * 文件id
     */
//    @Indexed
    private String fileId;

    /**
     * 点赞目标用户信息
     */
    private AccountInfo targetAcc;

    /**
     * 点赞发起者用户信息
     */
    private AccountInfo ownerAcc;

    /**
     * 点赞时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
