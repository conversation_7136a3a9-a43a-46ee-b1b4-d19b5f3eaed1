package com.lx.pl.db.mysql.gen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lx.pl.db.mysql.gen.entity.ExploreFile;
import com.lx.pl.dto.ExploreFileListResult;

public interface ExploreFileMapper extends BaseMapper<ExploreFile> {

    Page<ExploreFileListResult> getExploreFileList(Page<?> page);

    Integer getExploreFileListTotal();
}
