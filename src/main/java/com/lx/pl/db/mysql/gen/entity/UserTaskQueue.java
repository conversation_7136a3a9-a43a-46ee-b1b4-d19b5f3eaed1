package com.lx.pl.db.mysql.gen.entity;

import lombok.Data;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Data
@ToString
@Document(collection = "user_task_queue")
public class UserTaskQueue {

    @Id
    private String id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户账号
     */
    private String loginName;

    /**
     * 用户生图入参
     */
    private String genInfo;

    /**
     * 生图类型
     */
    private String featuresTypeValue;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
