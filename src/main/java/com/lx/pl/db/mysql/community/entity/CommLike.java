package com.lx.pl.db.mysql.community.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 社区图片点赞信息
 */
@Data
@Document(collection = "likes")
public class CommLike {

    @Id
    private String id;

    /**
     * 文件id
     */
//    @Indexed
    private String fileId;

    /**
     * 点赞目标用户信息
     */
    private AccountInfo targetAcc;

    /**
     * 点赞发起者用户信息
     */
    private AccountInfo ownerAcc;

    /**
     * 生图提示词
     */
    private String prompt;

    /**
     * 图片标签
     */
    private List<String> tags;

    /**
     * 目标用户是否已读
     */
    private Boolean read;

    /**
     * 点赞时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 小图路径
     */
    private String miniThumbnailUrl;
}
