package com.lx.pl.db.mysql.community.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

/**
 * 社区举报信息
 */
@Data
@Document(collection = "comment_reports")
public class CommCommentReport {

    @Id
    private String id;

    /**
     * 评论id
     */
//    @Indexed
    private String commentId;

    /**
     * 文件id
     */
//    @Indexed
    private String fileId;

    /**
     * 举报发起者用户信息
     */
    private AccountInfo ownerAcc;

    /**
     * 举报原因枚举
     */
    private Integer auditType;

    /**
     * 其他原因具体内容
     */
    private String otherContent;

    /**
     * 举报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
