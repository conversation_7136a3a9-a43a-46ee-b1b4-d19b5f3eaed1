package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 微信交易流水表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-21
 */
@TableName("gpt_wx_trade_order")
public class WxTradeOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 发起交易用户id
     */
    private Long userId;

    /**
     * 发起交易用户微信openid
     */
    private String wechatOpenId;

    /**
     * 商户自定义微信支付订单号
     */
    private String orderNo;

    /**
     * 本交易对应业务订单id（预付费模式，暂不使用此字段）
     */
    private Long tradeOrderId;

    /**
     * 微信端支付订单号
     */
    private String wechatTransactionId;

    /**
     * 支付类型（1.微信小程序 2.扫码支付 ）
     */
    private Integer payType;

    /**
     * 订单支付状态（1.已创建 2.交易已成功 3.交易已失败）
     */
    private Integer payStatus;

    /**
     * 订单金额（分）
     */
    private Long amount;

    /**
     * 交易类型（1.充值 2.退款 3.商家转账到余额（提现）4.初装费预付 5.套餐费 6.订单）
     */
    private Integer tradeType;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 创建时间
     */
    private LocalDateTime createDatetime;

    /**
     * 创建人
     */
    private Long createUserId;

    /**
     * 修改时间
     */
    private LocalDateTime updateDatetime;

    /**
     * 修改人
     */
    private LocalDateTime updateUserId;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getWechatOpenId() {
        return wechatOpenId;
    }

    public void setWechatOpenId(String wechatOpenId) {
        this.wechatOpenId = wechatOpenId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Long getTradeOrderId() {
        return tradeOrderId;
    }

    public void setTradeOrderId(Long tradeOrderId) {
        this.tradeOrderId = tradeOrderId;
    }

    public String getWechatTransactionId() {
        return wechatTransactionId;
    }

    public void setWechatTransactionId(String wechatTransactionId) {
        this.wechatTransactionId = wechatTransactionId;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    public Integer getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Integer payStatus) {
        this.payStatus = payStatus;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public Integer getTradeType() {
        return tradeType;
    }

    public void setTradeType(Integer tradeType) {
        this.tradeType = tradeType;
    }

    public LocalDateTime getPayTime() {
        return payTime;
    }

    public void setPayTime(LocalDateTime payTime) {
        this.payTime = payTime;
    }

    public LocalDateTime getCreateDatetime() {
        return createDatetime;
    }

    public void setCreateDatetime(LocalDateTime createDatetime) {
        this.createDatetime = createDatetime;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public LocalDateTime getUpdateDatetime() {
        return updateDatetime;
    }

    public void setUpdateDatetime(LocalDateTime updateDatetime) {
        this.updateDatetime = updateDatetime;
    }

    public LocalDateTime getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(LocalDateTime updateUserId) {
        this.updateUserId = updateUserId;
    }

    @Override
    public String toString() {
        return "WxTradeOrder{" +
                "id=" + id +
                ", userId=" + userId +
                ", wechatOpenId=" + wechatOpenId +
                ", orderNo=" + orderNo +
                ", tradeOrderId=" + tradeOrderId +
                ", wechatTransactionId=" + wechatTransactionId +
                ", payType=" + payType +
                ", payStatus=" + payStatus +
                ", amount=" + amount +
                ", tradeType=" + tradeType +
                ", payTime=" + payTime +
                ", createDatetime=" + createDatetime +
                ", createUserId=" + createUserId +
                ", updateDatetime=" + updateDatetime +
                ", updateUserId=" + updateUserId +
                "}";
    }
}
