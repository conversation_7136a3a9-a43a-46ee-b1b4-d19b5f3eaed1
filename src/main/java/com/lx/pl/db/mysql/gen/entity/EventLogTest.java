package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("gpt_event_log_test")
public class EventLogTest extends MyBaseEntity {
    @TableId(value = "id")
    private String id;

    /**
     * 用户行为
     */
    private String action;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户使用终端
     */
    private String userAgent;

    /**
     * 日志内容
     */
    private String content;

    /**
     * 用户操作ip
     */
    private String operateIp;

    /**
     * ip所属国家
     */
    private String ipCountry;
}
