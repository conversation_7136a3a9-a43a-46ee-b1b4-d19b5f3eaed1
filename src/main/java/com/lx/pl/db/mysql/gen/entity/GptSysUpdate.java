package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import org.springframework.data.annotation.Transient;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName("gpt_sys_update")
public class GptSysUpdate extends MyBaseEntity {

    @TableId
    private Long id;

    /**
     * 标题信息
     */
    private String title;

    /**
     * 简介
     */
    private String introduction;

    /**
     * 详情
     */
    private String details;

    /**
     * 发布状态（0：未发布，1：已发布）
     */
    private Boolean publish;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 来源平台： web android ios
     */
    private String platform;

    /**
     * 目标用户是否已读
     */
    @TableField(exist = false)
    private Boolean read;
}
