package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName("gpt_user_collect_classify")
@Data
public class UserCollectClassify extends MyBaseEntity {

    /**
     * 用户ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户名称
     */
    private String loginName;

    /**
     * 收藏夹名称
     */
    private String collectName;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 是否被删除：0：未删除 1：已删除
     */
    private Boolean del;

    /**
     * 描述
     */
    private String description;

    /**
     * 收藏数量
     */
    private Integer collectNums;

    /**
     * 收藏夹占用空间 单位：字节
     */
    private Long size;

    /**
     * 封面
     */
    private String cover;
}
