package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 账户交易记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-21
 */
@TableName("gpt_trade_record")
public class TradeRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 发起交易的用户id
     */
    private Long userId;

    /**
     * 交易金额，单位为分，正数为增加，负数为减少
     */
    private Long amount;

    /**
     * 交易类型（1.微信充值 2.消费 3.微信退款 4.微信用户提现 5.系统手动充值 6.系统手动扣款 7.微信订单支付 8.微信套餐费用支付 9.邀请奖励）
     */
    private Integer type;

    /**
     * 交易备注
     */
    private String remark;

    /**
     * 交易时间
     */
    private LocalDateTime createDatetime;

    /**
     * 操作人id
     */
    private Long creator;

    /**
     * 交易前的账户余额值快照值
     */
    private Long snapshotBalance;

    /**
     * 交易业务单号
     */
    private String orderNo;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getAmount() {
        return amount;
    }

    public void setAmount(Long amount) {
        this.amount = amount;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getCreateDatetime() {
        return createDatetime;
    }

    public void setCreateDatetime(LocalDateTime createDatetime) {
        this.createDatetime = createDatetime;
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Long getSnapshotBalance() {
        return snapshotBalance;
    }

    public void setSnapshotBalance(Long snapshotBalance) {
        this.snapshotBalance = snapshotBalance;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    @Override
    public String toString() {
        return "TradeRecord{" +
                "id=" + id +
                ", userId=" + userId +
                ", amount=" + amount +
                ", type=" + type +
                ", remark=" + remark +
                ", createDatetime=" + createDatetime +
                ", creator=" + creator +
                ", snapshotBalance=" + snapshotBalance +
                ", orderNo=" + orderNo +
                "}";
    }
}
