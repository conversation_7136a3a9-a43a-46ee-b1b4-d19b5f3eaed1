package com.lx.pl.db.mysql.community.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.annotation.Transient;

@Data
public class CommFileDetail extends CommFile {

    @Schema(description = "模型名称")
    private String modelDisplay;

    @Schema(description = "模型图标")
    private String modelAvatar;

    /**
     * 自己对用户是否已经关注
     */
    @Transient
    private Boolean followed = Boolean.FALSE;
}
