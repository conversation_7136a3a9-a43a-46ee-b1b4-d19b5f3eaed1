package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName("gpt_custom_file")
@Data
public class CustomFile extends MyBaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    private Long uid;


    private String promptId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String loginName;

    /**
     * 文件路径
     */
    private String imgUrl;

    /**
     * 缩略图路径
     */
    private String thumbnailUrl;


    /**
     * 高清缩略图名称Url
     */
    private String highThumbnailUrl;

    /**
     * 图片的宽
     */
    private int width;

    /**
     * 图片的高
     */
    private int height;


}
