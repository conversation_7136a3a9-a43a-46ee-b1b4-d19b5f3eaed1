package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = "log_record_address")
public class RecordAddress {

    @Id
    private String id;

    /**
     * 标记id
     */
    private String markId;

    /**
     * 地址id
     */
    private String addressId;


    private String promptId;

    /**
     * 是否属于fastHour机制内的任务
     */
    private Boolean fastHour;

    private String loginName;

    /**
     * 服务器处理ip（java）
     */
    private String serverIp;

    /**
     * 生图原始操作
     */
    private String originCreate;

    /**
     * 模型id
     */
    private String modelId;
}
