package com.lx.pl.db.mysql.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

@Component
public interface PicLumenMapper {

    @Select(
            "<script>" +
                    "SELECT\n"
                    + "\tsum( amount ) AS commission \n"
                    + "FROM\n"
                    + "\tgpt_divide_commission_record \n"
                    + "WHERE\n"
                    + "\treceive_user_id = ${receive_user_id}"
                    + "</script>")
    Long getCommissionByReceivedUserId(@Param("receive_user_id") long receiveUserId);

    @Select(
            "<script>" +
                    "SELECT\n"
                    + "\tsum( token_count ) AS commission \n"
                    + "FROM\n"
                    + "\tgpt_divide_commission_record \n"
                    + "WHERE\n"
                    + "\treceive_user_id = ${receive_user_id}"
                    + "</script>")
    Long getCommissionTokenCountByReceivedUserId(@Param("receive_user_id") long receiveUserId);
}
