package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@TableName("gpt_export_gen_info")
public class ExportGenInfo extends MyBaseEntity {

    /**
     * id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户账号
     */
    private String loginName;

    /**
     * 用户接收邮箱
     */
    private String email;

    /**
     * 是否已经导出发给用户： 0 ：否   1 ：是
     */
    private Boolean exportFlag;
}