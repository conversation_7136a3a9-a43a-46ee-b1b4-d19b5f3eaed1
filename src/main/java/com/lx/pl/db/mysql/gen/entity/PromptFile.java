package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@TableName("gpt_prompt_file")
@Data
public class PromptFile extends MyBaseEntity {

    @TableId(value = "id")
    private Long id;

    /**
     * 用户名称
     */
    private String loginName;

    private String promptId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 缩略图名称
     */
    private String thumbnailName;

    /**
     * 高清缩略图名称
     */
    private String highThumbnailName;

    /**
     * 文件路径 90%
     */
    private String fileUrl;

    /**
     * 缩略图路径 30%
     */
    private String thumbnailUrl;

    /**
     * 高清缩略图路径 70%
     */
    private String highThumbnailUrl;

    /**
     * 小图路径 15% size: 33%
     */
    private String miniThumbnailUrl;

    /**
     * 30% 高清图
     */
    private String highMiniUrl;

    /**
     * 敏感信息：涉黄等
     */
    private String sensitiveMessage;

    /**
     * 生成图片的宽
     */
    private int width;

    /**
     * 生成图片的高
     */
    private int height;

    /**
     * 占用空间 单位：字节
     */
    private Long size;

    /**
     * 图片点赞数
     */
    private Integer likeNums;

    /**
     * 收藏数
     */
    private Integer collectNums;


    /**
     * 是否被删除：0：未删除 1：已删除
     */
    private boolean del;

    @Schema(description = "是否公开(0:未公开 1:已公开 2:审核中  3.已拒绝)")
    private Integer isPublic;

    @Schema(description = "file来源类型")
    private String originCreate;

    /**
     * 拒绝内容描述
     */
    @Schema(description = "拒绝内容描述")
    private String rejectionContent;

    public boolean getDel() {
        return del;
    }

    public void setDel(boolean del) {
        this.del = del;
    }
}
