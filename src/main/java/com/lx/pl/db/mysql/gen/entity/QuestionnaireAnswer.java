package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("questionnaire_answer")
@Schema(description = "用户问卷调查回答表")
public class QuestionnaireAnswer extends MyBaseEntity {

    @Schema(description = "主键 ID")
    @TableId(value = "id")
    private Long id;

    @Schema(description = "用户登录名")
    private String loginName;

    @Schema(description = "问卷 ID")
    private String questionnaireId;

    @Schema(description = "问卷标题")
    private String questionnaireTitle;

    @Schema(description = "平台信息")
    private String platform;

    @Schema(description = "单选题1答案")
    private String scAnswer1;

    @Schema(description = "单选题2答案")
    private String scAnswer2;

    @Schema(description = "单选题3答案")
    private String scAnswer3;

    @Schema(description = "单选题4答案")
    private String scAnswer4;

    @Schema(description = "单选题5答案")
    private String scAnswer5;

    @Schema(description = "单选题6答案")
    private String scAnswer6;

    @Schema(description = "单选题7答案")
    private String scAnswer7;

    @Schema(description = "单选题8答案")
    private String scAnswer8;

    @Schema(description = "单选题9答案")
    private String scAnswer9;

    @Schema(description = "单选题10答案")
    private String scAnswer10;

    @Schema(description = "多选题1答案")
    private String mcAnswer1;

    @Schema(description = "多选题2答案")
    private String mcAnswer2;

    @Schema(description = "多选题3答案")
    private String mcAnswer3;

    @Schema(description = "多选题4答案")
    private String mcAnswer4;

    @Schema(description = "多选题5答案")
    private String mcAnswer5;

    @Schema(description = "多选题6答案")
    private String mcAnswer6;

    @Schema(description = "多选题7答案")
    private String mcAnswer7;

    @Schema(description = "多选题8答案")
    private String mcAnswer8;

    @Schema(description = "多选题9答案")
    private String mcAnswer9;

    @Schema(description = "多选题10答案")
    private String mcAnswer10;

    @Schema(description = "问答题1答案")
    private String essayAnswer1;

    @Schema(description = "问答题2答案")
    private String essayAnswer2;

    @Schema(description = "问答题3答案")
    private String essayAnswer3;

    @Schema(description = "问答题4答案")
    private String essayAnswer4;

    @Schema(description = "问答题5答案")
    private String essayAnswer5;

    @Schema(description = "评分题1答案")
    private String gradeAnswer1;

    @Schema(description = "评分题2答案")
    private String gradeAnswer2;

    @Schema(description = "评分题3答案")
    private String gradeAnswer3;

    @Schema(description = "评分题4答案")
    private String gradeAnswer4;

    @Schema(description = "评分题5答案")
    private String gradeAnswer5;

    @Schema(description = "删除标志（0:未删除 1:已删除）")
    @TableLogic
    private Boolean del;
}
