package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户图像生成记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-21
 */
@TableName("gpt_image_gen_record")
@Data
public class ImageGenRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    private Long userId;

    /**
     * 用户账号
     */
    private String loginName;

    /**
     * 图片名称
     */
    private String fileName;

    /**
     * 图片缩略图文件名部分后缀
     */
    private String thumbnailSuffix;

    /**
     * 下载路径前缀
     */
    private String downloadUrlPrefix;

    /**
     * 图片类型，jpg，png，webp等
     */
    private String fileType;

    /**
     * NSFW检测信息
     */
    private String nsfwInfo;

    /**
     * 创建日期
     */
    private LocalDateTime createTimestamp;

    /**
     * 更新日期
     */
    private LocalDateTime updateTimestamp;

    /**
     * 文件大小
     */
    private Integer length;

    /**
     * 生图信息
     */
    private JsonNode genInfo;

    private String promptId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 排队序号
     */
    private Integer taskNumber;

}
