package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;

@Data
@TableName("kpi_mix")
public class KpiMix extends MyBaseEntity {

    /**
     * id
     */
    private Long id;

    /**
     * 日期
     */
    private LocalDate recordDate;

    /**
     * 平均生图时间
     */
    private Double avgChartTime;

    /**
     * 平均等待时长
     */
    private Double avgWait;

    /**
     * 1图平均生图时间
     */
    private Double avgChartOneTime;

    /**
     * 2图平均生图时间
     */
    private Double avgChartTwoTime;

    /**
     * 3图平均生图时间
     */
    private Double avgChartThreeTime;

    /**
     * 4图平均生图时间
     */
    private Double avgChartFourTime;

    /**
     * 生图数量
     */
    private Long chartCount;

    /**
     * 日活
     */
    private Long dau;

    /**
     * 周活
     */
    private Long wau;

    /**
     * 月活
     */
    private Long mau;

    /**
     * 新注册用户量
     */
    private Long newRegisters;

    /**
     * 总用户量
     */
    private Long totalRegisters;

    /**
     * 单用户平均生图数量
     */
    private Long avgChartsPerUser;

    /**
     * 当日最大并发生图任务数
     */
    private Long maxConcurrentChartTasks;

    /**
     * 单用户最大生图数量
     */
    private Long maxChartsPerUser;

    /**
     * 生成单图任务数
     */
    private Long chartCountOne;

    /**
     * 生成2图任务数
     */
    private Long chartCountTwo;

    /**
     * 生成3图任务数
     */
    private Long chartCountThree;

    /**
     * 生成4图任务数
     */
    private Long chartCountFour;

    /**
     * 文生图任务数
     */
    private Long text2picTasks;

    /**
     * 高清修复任务数
     */
    private Long hiresfixTasks;

    /**
     * 去背景任务数
     */
    private Long removebgTasks;

    /**
     * 图生图任务数
     */
    private Long pic2picTasks;

    /**
     * 快速生图任务数
     */
    private Long fastTasks;

    /**
     * 生图任务数量
     */
    private Long chartTaskCount;

    /**
     * 图生图character_ref次数
     */
    private Long pic2picCharacterRef;

    /**
     * 图生图content_ref次数
     */
    private Long pic2picContentRef;

    /**
     * 图生图style_ref次数
     */
    private Long pic2picStyleRef;

    /**
     * 最喜爱生图比例
     */
    private String favoriteAspectRatio;

    /**
     * realistic生图数量
     */
    private Long realisticCount;

    /**
     * anime生图数量
     */
    private Long animeCount;

    /**
     * lineart生图数量
     */
    private Long lineartCount;
}
