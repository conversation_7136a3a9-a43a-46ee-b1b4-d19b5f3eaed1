package com.lx.pl.db.mysql.community.activity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lx.pl.db.mysql.gen.entity.MyBaseEntity;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("comm_activity_reward_settings")
public class CommActivityRewardSettings extends MyBaseEntity {

    /**
     * 配置ID
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 获得奖项
     */
    private Integer prizeLevel;

    /**
     * 奖励类型：1-Lumens，2-VIP
     */
    private String type;

    /**
     * 会员类型：basic-非会员，standard-普通会员，pro-高级会员
     */
    private String planLevel;

    /**
     * 时间区间：year, month
     */
    private String priceInterval;

    /**
     * 奖励有效开始时间
     */
    private LocalDateTime startTime;

    /**
     * 奖励有效结束时间
     */
    private LocalDateTime endTime;

    /**
     * 奖励数量
     */
    private Integer rewardNum;

    /**
     * 获奖人数
     */
    private Integer winnersNum;

    /**
     * 奖项描述
     */
    private String mark;
}