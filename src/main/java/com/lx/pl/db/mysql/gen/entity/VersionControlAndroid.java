package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName("gpt_version_control_android")
@Data
public class VersionControlAndroid extends MyBaseEntity {

    /**
     * 用户ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 版本号
     */
    private String version;

    /**
     * 是否在线版本（0否 1是）
     */
    private Integer isCurrent;

    /**
     * 升级类型（1强制更新 2强提示更新 3弱提示更新）
     */
    private Integer upgradeType;

}
