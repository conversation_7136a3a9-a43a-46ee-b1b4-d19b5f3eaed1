package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

@TableName("gpt_public_file_review")
@Data
public class PublicFileReview extends MyBaseEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户名称
     */
    private String loginName;

    private String promptId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 状态(review(审核中),pass(通过),rejection(拒绝))
     */
    private String reviewStatus;

    /**
     * 拒绝类型（Violence(暴力) ，Pornography（色情）， Racial discrimination(种族歧视) ， Copyright infringement (版权)， Other（其他））
     */
    private String isDisplay;

    /**
     * 拒绝内容描述
     */
    private String rejectionContent;


    /**
     * 是否被删除：0：未删除 1：已删除
     */
    private Boolean del;

    /**
     * 文件路径
     */
    private String fileUrl;

    /**
     * 缩略图路径
     */
    private String thumbnailUrl;

    /**
     * 高清缩略图路径
     */
    private String highThumbnailUrl;

    /**
     * 小图路径
     */
    private String miniThumbnailUrl;

    /**
     * 30% 高清图
     */
    private String highMiniUrl;

    /**
     * 简要
     */
    private String brief;

    /**
     * 活动id
     */
    private String activityId;

    /**
     * 公开类型：everyone ： 所有人可见  myself ： 自己可见  fullLikes : 满足20点赞后可见
     */
    private String publicType;

    /**
     * 图片id
     */
    private String fileId;

    /**
     * 正向提示词
     */
    private String prompt;
    /**
     * 图片结果宽度
     */
    private Integer realWidth;

    /**
     * 图片结果高度
     */
    private Integer realHeight;

    /**
     * 生图信息
     */
    private JsonNode genInfo;

    /**
     * 模型名称
     */
    private String modelDisplay;

}
