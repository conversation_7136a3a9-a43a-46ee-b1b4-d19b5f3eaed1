package com.lx.pl.db.mysql.community.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Data
@Document(collection = "follows")
public class CommFollow {
    @Id
    private String id;
    /**
     * 发起关注者用户信息
     */
    private AccountInfo ownerAcc;

    /**
     * 发起关注者用户的自我介绍
     */
    private String ownerIntroduction;

    /**
     * 被关注者用户信息
     */
    private AccountInfo targetAcc;

    /**
     * 被关注者用户的自我介绍
     */
    private String targetIntroduction;

    /**
     * 关注时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 自己是否已经关注过粉丝
     */
    @Transient
    private Boolean followed = Boolean.FALSE;

}