package com.lx.pl.db.mysql.gen.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;

import java.time.LocalDateTime;

@TableName("gpt_user_collect")
@Data
public class UserCollect extends MyBaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户名称
     */
    private String loginName;

    private String promptId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 收藏夹id
     */
    private Long classifyId;

    /**
     * 文件名称
     */
    private String fileName;


    /**
     * 缩略图名称
     */
    private String thumbnailName;

    /**
     * 高清缩略图名称
     */
    private String highThumbnailName;

    /**
     * 文件路径
     */
    private String fileUrl;

    /**
     * 缩略图路径
     */
    private String thumbnailUrl;

    /**
     * 高清缩略图路径
     */
    private String highThumbnailUrl;
    /**
     * 小图路径
     */
    private String miniThumbnailUrl;
    /**
     * 敏感信息：涉黄等
     */
    private String sensitiveMessage;

    /**
     * 生成图片的宽
     */
    private int width;

    /**
     * 生成图片的高
     */
    private int height;


    /**
     * 正向提示词
     */
    private String prompt;

    /**
     * 文件id
     */
    private Long fileId;

    /**
     * 生图信息
     */
    private JsonNode genInfo;

    /**
     * 生图原始操作
     */
    private String originCreate;

    /**
     * 占用空间
     */
    private Long size;

    /**
     * 是否被删除：0：未删除 1：已删除
     */
    private boolean del;

    public boolean getDel() {
        return del;
    }

    public void setDel(boolean del) {
        this.del = del;
    }
}
