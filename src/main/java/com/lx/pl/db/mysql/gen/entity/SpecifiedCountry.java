package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName("gpt_specified_country")
@Data
public class SpecifiedCountry extends MyBaseEntity {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 国家名称英文
     */
    private String country;

    /**
     * 国家名称中文
     */
    private String countryName;

    /**
     * 人均GDP
     */
    private String gdp;

    /**
     * 是否被删除：0：未删除 1：已删除
     */
    private boolean del;

    public boolean getDel() {
        return del;
    }

    public void setDel(boolean del) {
        this.del = del;
    }
}
