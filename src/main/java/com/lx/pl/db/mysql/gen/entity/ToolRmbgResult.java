package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;

import java.time.LocalDateTime;

/**
 * 批量去背景结果表实体类
 */
@TableName("tool_rmbg_result")
@Data
@ToString
@JsonIgnoreProperties({"failureMessage"})
public class ToolRmbgResult extends MyBaseEntity {

    @Id
    private Long id;

    @Schema(description = "用户账号")
    private String loginName;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "批次ID")
    private String batchId;

    @Schema(description = "上传图片路径")
    private String upFileUrl;

    @Schema(description = "上传图片宽度")
    private Integer upWidth;

    @Schema(description = "上传图片高度")
    private Integer upHeight;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "上传时间")
    private LocalDateTime upTime;

    @Schema(description = "任务状态：0:未处理 1：处理中 2：处理完毕 3处理失败")
    private Integer status;

    @Schema(description = "任务失败信息")
    private String failureMessage;

    @Schema(description = "结果图片路径")
    private String fileUrl;

    @Schema(description = "是否删除：0:未删除 1：已删除")
    @Transient
    private Boolean del;

}