package com.lx.pl.db.mysql.gen.mapper;

import com.lx.pl.db.mysql.gen.entity.UserCollect;
import com.lx.pl.db.mysql.gen.entity.UserCollectClassify;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserCollectClassifyMapper extends BaseMapper<UserCollectClassify> {

   Integer updateSizeAndNumAdd(Long classifyId, String loginName,Integer num);

   Integer  updateSizeAndNumReduce(Long classifyId, String loginName,Integer num);


}
