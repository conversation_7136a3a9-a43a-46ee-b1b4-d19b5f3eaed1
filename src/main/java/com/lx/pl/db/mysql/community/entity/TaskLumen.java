package com.lx.pl.db.mysql.community.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Set;

@Data
@Document(collection = "task_lumen")
public class TaskLumen {

    @Id
    private String id;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "用户账号")
    private String userLoginName;

    @Schema(description = "首次生图数量")
    private Integer creationNums;

    @Schema(description = "点赞数量")
    private Integer likeNums;

    @Schema(description = "关注用户数量")
    private Integer followNums;

    @Schema(description = "收到的点赞数量")
    private Integer receiveLikeNums;

    @Schema(description = "收到的关注数量")
    private Integer receiveFollowNums;

    @Schema(description = "关注YouTube")
    private Integer subscribeYoutube;

    @Schema(description = "加入Discord")
    private Integer joinDiscord;

    @Schema(description = "为PicLumen撰写评论")
    private Integer writeReview;

    @Schema(description = "关注IG官号")
    private Integer followIG;

    @Schema(description = "关注X官号")
    private Integer followX;

    @Schema(description = "已经完成任务id集合")
    private Set<String> finishTaskIds;

    @Schema(description = "已经领取lumen任务id集合")
    private Set<String> rewardTaskIds;

    @Schema(description = "为PicLumen撰写Producthunt评论")
    private Integer writeReviewProducthunt;
}
