package com.lx.pl.db.mysql.gen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lx.pl.db.mysql.gen.entity.PromptFile;
import com.lx.pl.dto.CommHistoryFile;
import com.lx.pl.dto.CommHistoryLike;
import com.lx.pl.dto.PromptFilePageResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface PromptFileMapper extends BaseMapper<PromptFile> {

    Page<PromptFilePageResult> getPromptFileList(Page<?> page, @Param("loginName") String loginName, @Param("vagueKey") String vagueKey
            , @Param("collationName") String collationName, List<String> opexLoginNameList);

    Double getTaskAvgTime(@Param("startDate") String startDate, @Param("endDate") String endDate, List<String> opexLoginNameList);

    Long getCreatePictureNums(@Param("startDate") String startDate, @Param("endDate") String endDate, List<String> opexLoginNameList);

    Long getUserAvgCratePictureNums(@Param("startDate") String startDate, @Param("endDate") String endDate, List<String> opexLoginNameList);

    Long getUserMaxCratePictureNums(@Param("startDate") String startDate, @Param("endDate") String endDate, List<String> opexLoginNameList);

    List<CommHistoryFile> getExplorePromptFileList(@Param("lastId") Long lastId, @Param("batchSize") Integer batchSize, @Param("tableName") String tableName);

    List<CommHistoryLike> getUserLikePromptFileListByCursor(@Param("lastId") Long lastId, @Param("batchSize") Integer batchSize, @Param("tableName") String tableName);

    List<String> queryHasNoFilePromptId(@Param("promptIds") Set<String> promptIds, @Param("loginName") String loginName);

    // 批量更新文件大小
    void batchUpdate(List<PromptFile> files,String tableName);

}
