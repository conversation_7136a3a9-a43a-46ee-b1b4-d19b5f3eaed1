package com.lx.pl.db.mysql.gen.mapper;

import com.lx.pl.db.mysql.gen.entity.User;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lx.pl.dto.StatisticsUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-21
 */
public interface UserMapper extends BaseMapper<User> {

    Long getNewUserNums(@Param("startDate") String startDate, @Param("endDate") String endDate);

    Long getUserNums(@Param("startDate") String startDate, @Param("endDate") String endDate, List<String> opexLoginNameList);

    List<StatisticsUser> getUserTopPictureNums(@Param("startDate") String startDate, @Param("endDate") String endDate);

    List<String> getUserIdByLoginNames(List<String> opexLoginNameList);

    void batchCollectionUpdateUsers(@Param("users") List<User> users);


}
