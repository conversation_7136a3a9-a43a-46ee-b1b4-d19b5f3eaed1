package com.lx.pl.db.mysql.community.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

/**
 * 社区评论信息
 */
@Data
@Document(collection = "comments")
public class CommComment {
    @Id
    private String id;

    /**
     * 评论点赞数
     */
    private Integer commentLikeNums;

    /**
     * 回复数
     */
    private Integer replyNums;

    /**
     * 文件id
     */
//    @Indexed
    private String fileId;

    /**
     * 顶级评论id
     */
    private String firstCommentId;

    /**
     * 目标评论id
     */
//    @Indexed
    private String commentId;

    /**
     * 评论目标用户信息
     */
    private AccountInfo targetAcc;

    /**
     * 评论发起者用户信息
     */
    private AccountInfo ownerAcc;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 目标用户是否已读
     */
    private Boolean read;

    /**
     * 自己对评论是否点赞
     */
    @Transient
    private Boolean liked = Boolean.FALSE;

    /**
     * 自己对评论是否举报
     */
    @Transient
    private Boolean reported = Boolean.FALSE;

    /**
     * 评论时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 小图路径
     */
    private String miniThumbnailUrl;

}