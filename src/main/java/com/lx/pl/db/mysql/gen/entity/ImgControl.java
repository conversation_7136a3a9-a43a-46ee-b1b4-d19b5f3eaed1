package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@TableName("gpt_img_control")
@Data
public class ImgControl extends MyBaseEntity {

    @TableId
    private Long id;

    @Schema(description = "用户 ID")
    private Long userId;

    @Schema(description = "用户账号")
    private String loginName;

    @Schema(description = "图片地址")
    private String imgUrl;

    @Schema(description = "图片高度")
    private Integer height;

    @Schema(description = "图片宽度")
    private Integer width;

    @Schema(description = "真实宽")
    private Integer realWidth;

    @Schema(description = "真实高")
    private Integer realHeight;

    @Schema(description = "图片类型（public: 公共的, private: 私有的）")
    private String imgType;

    @Schema(description = "控制类型（canny: 主体, depth: 轮廓, openpose: 姿势）")
    private String controlType;

    @Schema(description = "逻辑删除（0: 未删除, 1: 已删除）")
    private Boolean del;
}
