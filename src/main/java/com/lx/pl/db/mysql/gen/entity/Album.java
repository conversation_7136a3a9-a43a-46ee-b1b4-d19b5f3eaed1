package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@TableName("gpt_user_album")
@Data
public class Album extends MyBaseEntity {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String loginName;

    /**
     * 文件名称
     */
    private String imgName;

    /**
     * 缩略图名称
     */
    private String thumbImgName;

    /**
     * 文件路径
     */
    private String imgUrl;

    /**
     * 缩略图路径
     */
    private String thumbImgUrl;

    /**
     * 算出的宽
     */
    private Integer width;

    /**
     * 算出的高
     */
    private Integer height;

    /**
     * 图片大小
     */
    private Long size;

    /**
     * 真实宽
     */
    private Integer realWidth;

    /**
     * 真实高
     */
    private Integer realHeight;
}
