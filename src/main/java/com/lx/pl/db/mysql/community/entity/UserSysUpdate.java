package com.lx.pl.db.mysql.community.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Data
@Document(collection = "user_sys_update")
public class UserSysUpdate {

    @Id
    private String id;


    /**
     * 用户信息
     */
    private AccountInfo accountInfo;


    /**
     * 系统更新id
     */
    private String sysUpdateId;

    /**
     * 来源平台： web android ios
     */
    private String platform;

    /**
     * 已读时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
