package com.lx.pl.db.mysql.gen.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lx.pl.dto.HistoryImgResult;
import com.lx.pl.dto.NotPublishImgResult;
import org.apache.ibatis.annotations.Param;

public interface HistoryImgMapper  extends BaseMapper<HistoryImgResult> {

    Page<HistoryImgResult> getHistoryList(Page<?> page, @Param("loginName") String loginName, @Param("vagueKey") String vagueKey
            , @Param("collationName") String collationName,String vipType, @Param("notCollection")  Boolean  notCollection);

    Page<NotPublishImgResult> getNotCollectionList(Page<?> page, @Param("loginName") String loginName, @Param("publicStatus") Integer publicStatus,
                                                   @Param("vipType")  String vipType);

}
