package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * <p>
 * 存储一些字典内容，用于网站配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-21
 */
@TableName("gpt_dict")
public class Dict implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 键
     */
    private String configKey;

    /**
     * 键值
     */
    private String configValue;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getConfigKey() {
        return configKey;
    }

    public void setConfigKey(String configKey) {
        this.configKey = configKey;
    }

    public String getConfigValue() {
        return configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    @Override
    public String toString() {
        return "Dict{" +
                "id=" + id +
                ", configKey=" + configKey +
                ", configValue=" + configValue +
                "}";
    }
}
