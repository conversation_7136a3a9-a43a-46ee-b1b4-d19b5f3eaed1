package com.lx.pl.db.mysql.community.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

@Data
@Document(collection = "lumen_reward_log")
public class LumenRewardLog {

    @Id
    private String id;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "用户账号")
    private String userLoginName;

    @Schema(description = "任务id")
    private String taskId;

    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "任务领取lumen币")
    private Integer taskReward;

    @Schema(description = "领取lumen币时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
