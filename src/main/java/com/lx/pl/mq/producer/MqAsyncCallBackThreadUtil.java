 package com.lx.pl.mq.producer;

 import java.util.concurrent.LinkedBlockingQueue;
 import java.util.concurrent.ThreadFactory;
 import java.util.concurrent.ThreadPoolExecutor;
 import java.util.concurrent.TimeUnit;
 import java.util.concurrent.atomic.AtomicInteger;

 public class MqAsyncCallBackThreadUtil {

     public static final ThreadPoolExecutor MQ_THREAD_POOL = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors(),
                                                                                    Runtime.getRuntime().availableProcessors() + 1,
                                                                                    1,
                                                                                    TimeUnit.MINUTES,
                                                                                    new LinkedBlockingQueue<>(1000),
                                                                                    new CustomThreadFactory("asyncSend-mq-"),
                                                                                    new ThreadPoolExecutor.CallerRunsPolicy());
     static class CustomThreadFactory implements ThreadFactory {
         private final AtomicInteger threadNumber = new AtomicInteger(1);
         private final ThreadGroup group;
         private final String prefix;

         CustomThreadFactory(String prefix) {
             SecurityManager s = System.getSecurityManager();
             group = (s != null) ? s.getThreadGroup() : Thread.currentThread().getThreadGroup();
             this.prefix = prefix;
         }

         @Override
         public Thread newThread(Runnable runnable) {
             Thread t = new Thread(group, runnable, prefix + threadNumber.getAndIncrement());
             if (t.isDaemon()) {
                 t.setDaemon(false);
             }
             if (t.getPriority() != Thread.NORM_PRIORITY) {
                 t.setPriority(Thread.NORM_PRIORITY);
             }
             return t;
         }
     }
 }
