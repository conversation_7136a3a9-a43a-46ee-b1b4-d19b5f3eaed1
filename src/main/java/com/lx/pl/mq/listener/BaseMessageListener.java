package com.lx.pl.mq.listener;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.lx.pl.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseMessageListener implements RocketMQListener {

    private static final Logger logger = LoggerFactory.getLogger("rocketmq-msg");

    public abstract void doWork(MessageView message);

    @Override
    public ConsumeResult consume(MessageView message) {
        try {
            doWork(message);
            return ConsumeResult.SUCCESS;
        } catch (Exception e) {
            // 消费失败
            logger.error("consume error:", e);
            return ConsumeResult.FAILURE;
        }
    }

    public <T> T getBody(MessageView message, TypeReference<T> typeReference) {
        String result = getString(message);
        try {
            T t = JsonUtils.writeToTypeReference(result, typeReference);
            return t;
        } catch (JsonProcessingException e) {
            logger.error("getBody json parse error:", e);
            throw new RuntimeException(e);
        }
    }

    private static String getString(MessageView message) {
        Charset charset = StandardCharsets.UTF_8;
        ByteBuffer body = message.getBody();
        String result = charset.decode(body)
                .toString();
        body.clear();
        return result;
    }

    public <T> List<T> getListBody(MessageView message, Class<T> clazz) {
        String result = getString(message);
        List<T> tList = null;
        try {
            tList = JsonUtils.writeToList(result, clazz);
        } catch (JsonProcessingException e) {
            logger.error("getListBody json parse error:", e);
            throw new RuntimeException(e);
        }
        return tList;
    }

}
