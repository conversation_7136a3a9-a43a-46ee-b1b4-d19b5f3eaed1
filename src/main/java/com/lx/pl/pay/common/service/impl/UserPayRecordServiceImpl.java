package com.lx.pl.pay.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.pay.common.converter.UserPayRecordConverter;
import com.lx.pl.pay.common.domain.UserPayRecord;
import com.lx.pl.pay.common.domain.UserPayRecordItem;
import com.lx.pl.pay.common.dto.SavePaymentRecordRequest;
import com.lx.pl.pay.common.enums.PaymentPlatform;
import com.lx.pl.pay.common.mapper.UserPayRecordMapper;
import com.lx.pl.pay.common.service.PaymentIdempotencyService;
import com.lx.pl.pay.common.service.UserPayRecordItemService;
import com.lx.pl.pay.common.service.UserPayRecordService;
import com.lx.pl.pay.common.vo.UserPayRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户支付记录服务实现类
 */
@Slf4j
@Service
public class UserPayRecordServiceImpl extends ServiceImpl<UserPayRecordMapper, UserPayRecord> implements UserPayRecordService {

    @Autowired
    private UserPayRecordItemService userPayRecordItemService;

    @Autowired
    private PaymentIdempotencyService paymentIdempotencyService;

    @Override
    public List<UserPayRecord> getByUserId(Long userId) {
        return this.lambdaQuery()
                .eq(UserPayRecord::getUserId, userId)
                .orderByDesc(UserPayRecord::getCreateTime)
                .list();
    }

    @Override
    public List<UserPayRecord> getByLoginName(String loginName) {
        return this.lambdaQuery()
                .eq(UserPayRecord::getLoginName, loginName)
                .orderByDesc(UserPayRecord::getCreateTime)
                .list();
    }

    @Override
    public List<UserPayRecord> getByPlatform(Integer platform) {
        return this.lambdaQuery()
                .eq(UserPayRecord::getPlatform, platform)
                .orderByDesc(UserPayRecord::getCreateTime)
                .list();
    }

    @Override
    public List<UserPayRecord> getByUserIdAndPlatform(Long userId, Integer platform) {
        return this.lambdaQuery()
                .eq(UserPayRecord::getUserId, userId)
                .eq(UserPayRecord::getPlatform, platform)
                .orderByDesc(UserPayRecord::getCreateTime)
                .list();
    }

    @Override
    public CommPageInfo<UserPayRecord> getByUserIdWithCursor(Long userId, Long lastId, Integer pageSize, Integer platform) {
        LambdaQueryWrapper<UserPayRecord> queryWrapper = new LambdaQueryWrapper<>();

        // 基础条件：用户ID
        queryWrapper.eq(UserPayRecord::getUserId, userId);

        // 可选条件：平台类型
        if (platform != null) {
            queryWrapper.eq(UserPayRecord::getPlatform, platform);
        }

        // 游标分页：ID 小于 lastId
        if (lastId != null) {
            queryWrapper.lt(UserPayRecord::getId, lastId);
        }

        // 按ID降序排序（最新的记录在前）
        queryWrapper.orderByDesc(UserPayRecord::getId);

        // 限制查询数量
        queryWrapper.last("LIMIT " + pageSize);

        List<UserPayRecord> records = this.list(queryWrapper);

        // 构建分页结果
        CommPageInfo<UserPayRecord> pageInfo = new CommPageInfo<>();
        pageInfo.setResultList(records);
        pageInfo.setPageSize(pageSize);

        // 设置下一页的游标
        if (!CollectionUtils.isEmpty(records)) {
            UserPayRecord lastRecord = records.get(records.size() - 1);
            pageInfo.setLastId(String.valueOf(lastRecord.getId()));
        } else {
            pageInfo.setLastId("");
        }

        return pageInfo;
    }

    @Override
    public CommPageInfo<UserPayRecordVO> getByUserIdWithCursorVO(Long userId, Long lastId, Integer pageSize, Integer platform) {
        // 先获取支付记录
        CommPageInfo<UserPayRecord> recordPageInfo = getByUserIdWithCursor(userId, lastId, pageSize, platform);

        if (CollectionUtils.isEmpty(recordPageInfo.getResultList())) {
            CommPageInfo<UserPayRecordVO> voPageInfo = new CommPageInfo<>();
            voPageInfo.setResultList(List.of());
            voPageInfo.setPageSize(pageSize);
            voPageInfo.setLastId("");
            return voPageInfo;
        }

        // 获取所有记录的ID
        List<Long> recordIds = recordPageInfo.getResultList().stream()
                .map(UserPayRecord::getId)
                .collect(Collectors.toList());

        // 批量查询支付记录详情
        List<UserPayRecordItem> allItems = userPayRecordItemService.lambdaQuery()
                .in(UserPayRecordItem::getRecordId, recordIds)
                .list();

        // 按记录ID分组
        Map<Long, List<UserPayRecordItem>> itemsMap = allItems.stream()
                .collect(Collectors.groupingBy(UserPayRecordItem::getRecordId));

        // 转换为VO
        List<UserPayRecordVO> voList = UserPayRecordConverter.toVOList(recordPageInfo.getResultList(), itemsMap);

        // 构建分页结果
        CommPageInfo<UserPayRecordVO> voPageInfo = new CommPageInfo<>();
        voPageInfo.setResultList(voList);
        voPageInfo.setPageSize(recordPageInfo.getPageSize());
        voPageInfo.setLastId(recordPageInfo.getLastId());

        return voPageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserPayRecord savePaymentRecord(SavePaymentRecordRequest request) {
        // 1. 验证平台类型
        if (!PaymentPlatform.isValidCode(request.getPlatform())) {
            throw new IllegalArgumentException("Invalid platform code: " + request.getPlatform());
        }

//        // 2. 幂等校验
//        UserPayRecord existingRecord = this.checkIdempotency(request);
//        if (existingRecord != null) {
//            log.info("Payment record already exists, returning existing record: recordId={}, externalTransactionId={}",
//                    existingRecord.getId(), request.getExternalTransactionId());
//            return existingRecord;
//        }

        // 3. 创建支付记录
        UserPayRecord payRecord = new UserPayRecord();
        BeanUtils.copyProperties(request, payRecord);

        // 保存主记录
        payRecord.setCreateTime(LocalDateTime.now());
        boolean saved = this.save(payRecord);
        if (!saved) {
            throw new RuntimeException("Failed to save payment record");
        }

        log.info("Saved new payment record: recordId={}, platform={}, externalTransactionId={}, amount={}",
                payRecord.getId(), request.getPlatform(), request.getExternalTransactionId(), request.getAmount());

        // 4. 保存支付记录详情
        if (!CollectionUtils.isEmpty(request.getItems())) {
            List<UserPayRecordItem> items = request.getItems().stream()
                    .map(itemRequest -> {
                        UserPayRecordItem item = new UserPayRecordItem();
                        BeanUtils.copyProperties(itemRequest, item);

                        // 设置关联信息
                        item.setRecordId(payRecord.getId());
                        item.setLoginName(request.getLoginName());
                        item.setUserId(request.getUserId());
                        item.setPlatform(request.getPlatform());
                        item.setCreateTime(payRecord.getCreateTime());

                        return item;
                    })
                    .collect(Collectors.toList());

            boolean itemsSaved = userPayRecordItemService.saveBatch(items);
            if (!itemsSaved) {
                throw new RuntimeException("Failed to save payment record items");
            }

            log.info("Saved {} payment record items for recordId={}", items.size(), payRecord.getId());
        }

        return payRecord;
    }

    @Override
    public UserPayRecord checkIdempotency(SavePaymentRecordRequest request) {
        // 基础校验：平台 + 外部交易ID
        UserPayRecord existingRecord = checkByExternalTransactionId(request.getPlatform(), request.getExternalTransactionId());

        if (existingRecord != null) {
            log.info("Found existing payment record by externalTransactionId: platform={}, externalTransactionId={}, recordId={}",
                    request.getPlatform(), request.getExternalTransactionId(), existingRecord.getId());
            return existingRecord;
        }

        // 如果有外部订单号，进行双重校验
        if (StringUtils.hasText(request.getExternalOrderId())) {
            existingRecord = checkByExternalIds(request.getPlatform(), request.getExternalTransactionId(), request.getExternalOrderId());
            if (existingRecord != null) {
                log.info("Found existing payment record by externalIds: platform={}, externalTransactionId={}, externalOrderId={}, recordId={}",
                        request.getPlatform(), request.getExternalTransactionId(), request.getExternalOrderId(), existingRecord.getId());
                return existingRecord;
            }
        }

        return null;
    }

    @Override
    public UserPayRecord checkByExternalTransactionId(Integer platform, String externalTransactionId) {
        if (platform == null || !StringUtils.hasText(externalTransactionId)) {
            return null;
        }

        return this.lambdaQuery()
                .eq(UserPayRecord::getPlatform, platform)
                .eq(UserPayRecord::getExternalTransactionId, externalTransactionId)
                .one();
    }

    @Override
    public UserPayRecord checkByUserAndExternalTransactionId(Integer platform, Long userId, String externalTransactionId) {
        if (platform == null || userId == null || !StringUtils.hasText(externalTransactionId)) {
            return null;
        }

        return this.lambdaQuery()
                .eq(UserPayRecord::getPlatform, platform)
                .eq(UserPayRecord::getUserId, userId)
                .eq(UserPayRecord::getExternalTransactionId, externalTransactionId)
                .one();
    }

    @Override
    public UserPayRecord checkByExternalIds(Integer platform, String externalTransactionId, String externalOrderId) {
        if (platform == null || !StringUtils.hasText(externalTransactionId) || !StringUtils.hasText(externalOrderId)) {
            return null;
        }

        return this.lambdaQuery()
                .eq(UserPayRecord::getPlatform, platform)
                .eq(UserPayRecord::getExternalTransactionId, externalTransactionId)
                .eq(UserPayRecord::getExternalOrderId, externalOrderId)
                .one();
    }

    @Override
    public UserPayRecord checkStripeIdempotency(String invoiceId) {
        LambdaQueryWrapper<UserPayRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserPayRecord::getPlatform, PaymentPlatform.STRIPE.getCode());

        // 优先使用payment_intent_id
        if (StringUtils.hasText(invoiceId)) {
            queryWrapper.eq(UserPayRecord::getExternalTransactionId, invoiceId);
        } else {
            return null;
        }

        UserPayRecord record = this.getOne(queryWrapper);
        if (record != null) {
            log.info("Found existing Stripe payment record: invoiceId={}, recordId={}",
                    invoiceId, record.getId());
        }
        return record;
    }

    @Override
    public UserPayRecord checkPayPalIdempotency(String orderId, String subscriptionId, String payerId) {
        LambdaQueryWrapper<UserPayRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserPayRecord::getPlatform, PaymentPlatform.PAYPAL.getCode());

        // 优先使用order_id
        if (StringUtils.hasText(orderId)) {
            queryWrapper.eq(UserPayRecord::getExternalTransactionId, orderId);
        } else if (StringUtils.hasText(subscriptionId)) {
            queryWrapper.eq(UserPayRecord::getExternalTransactionId, subscriptionId);
        } else {
            return null;
        }

        // 如果有payerId，作为辅助校验
        if (StringUtils.hasText(payerId)) {
            queryWrapper.eq(UserPayRecord::getExternalOrderId, payerId);
        }

        UserPayRecord record = this.getOne(queryWrapper);
        if (record != null) {
            log.info("Found existing PayPal payment record: orderId={}, subscriptionId={}, payerId={}, recordId={}",
                    orderId, subscriptionId, payerId, record.getId());
        }
        return record;
    }

    @Override
    public UserPayRecord checkAppleIdempotency(String originalTransactionId, String transactionId) {
        if (!StringUtils.hasText(originalTransactionId)) {
            return null;
        }

        LambdaQueryWrapper<UserPayRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserPayRecord::getPlatform, PaymentPlatform.APPLE.getCode())
                .eq(UserPayRecord::getExternalTransactionId, originalTransactionId);

        // 如果有transactionId，作为辅助校验
        if (StringUtils.hasText(transactionId)) {
            queryWrapper.eq(UserPayRecord::getExternalOrderId, transactionId);
        }

        UserPayRecord record = this.getOne(queryWrapper);
        if (record != null) {
            log.info("Found existing Apple payment record: originalTransactionId={}, transactionId={}, recordId={}",
                    originalTransactionId, transactionId, record.getId());
        }
        return record;
    }

    @Override
    public UserPayRecord checkGoogleIdempotency(String purchaseToken, String orderId) {
        if (!StringUtils.hasText(purchaseToken)) {
            return null;
        }

        LambdaQueryWrapper<UserPayRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserPayRecord::getPlatform, PaymentPlatform.GOOGLE.getCode())
                .eq(UserPayRecord::getExternalTransactionId, purchaseToken);

        // 如果有orderId，作为辅助校验
        if (StringUtils.hasText(orderId)) {
            queryWrapper.eq(UserPayRecord::getExternalOrderId, orderId);
        }

        UserPayRecord record = this.getOne(queryWrapper);
        if (record != null) {
            log.info("Found existing Google payment record: purchaseToken={}, orderId={}, recordId={}",
                    purchaseToken, orderId, record.getId());
        }
        return record;
    }
}
