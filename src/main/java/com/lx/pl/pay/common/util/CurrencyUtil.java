package com.lx.pl.pay.common.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.Currency;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * 货币工具类，用于处理不同货币的转换和格式化
 */
public class CurrencyUtil {
    
    // 货币区域设置映射
    private static final Map<String, Locale> CURRENCY_LOCALES = new HashMap<>();
    
    static {
        // 设置常用货币的区域
        CURRENCY_LOCALES.put("USD", Locale.US);
        CURRENCY_LOCALES.put("EUR", Locale.FRANCE);
        CURRENCY_LOCALES.put("GBP", Locale.UK);
        CURRENCY_LOCALES.put("CNY", Locale.CHINA);
        CURRENCY_LOCALES.put("JPY", Locale.JAPAN);
        CURRENCY_LOCALES.put("SGD", new Locale("en", "SG")); // 新加坡
        CURRENCY_LOCALES.put("AUD", new Locale("en", "AU")); // 澳大利亚
        CURRENCY_LOCALES.put("CAD", Locale.CANADA);
    }

    /**
     * 将 milliunits 转换为实际货币金额
     * 
     * @param milliunits Apple 返回的 milliunits 金额
     * @return 实际金额
     */
    public static BigDecimal fromMilliunits(Long milliunits) {
        if (milliunits == null) {
            return null;
        }
        
        // milliunits 是千分之一单位，所以除以1000
        return new BigDecimal(milliunits).divide(new BigDecimal(1000), 3, RoundingMode.HALF_UP);
    }
    
    /**
     * 将实际金额转换为 milliunits
     * 
     * @param amount 实际金额
     * @return milliunits 金额
     */
    public static Long toMilliunits(BigDecimal amount) {
        if (amount == null) {
            return null;
        }
        
        // 转换为 milliunits，乘以1000
        return amount.multiply(new BigDecimal(1000)).longValue();
    }
    
    /**
     * 格式化 milliunits 金额为本地化货币字符串
     * 
     * @param milliunits milliunits 金额
     * @param currencyCode 货币代码
     * @return 格式化后的金额字符串
     */
    public static String formatCurrency(Long milliunits, String currencyCode) {
        if (milliunits == null || currencyCode == null) {
            return "";
        }
        
        BigDecimal actualAmount = fromMilliunits(milliunits);
        
        // 获取货币对应的区域设置
        Locale locale = CURRENCY_LOCALES.getOrDefault(currencyCode, Locale.US);
        
        // 创建货币格式化器
        NumberFormat currencyFormatter = NumberFormat.getCurrencyInstance(locale);
        currencyFormatter.setCurrency(Currency.getInstance(currencyCode));
        
        // 返回格式化后的货币字符串
        return currencyFormatter.format(actualAmount);
    }
    
    /**
     * 获取货币的示例价格表示
     * 
     * @param currencyCode 货币代码
     * @return 示例价格表示
     */
    public static String getCurrencyExample(String currencyCode) {
        if (currencyCode == null) {
            return "";
        }
        
        StringBuilder example = new StringBuilder();
        example.append("货币: ").append(currencyCode).append("\n");
        example.append("1 ").append(currencyCode).append(" = 1000 milliunits\n");
        
        // 添加一些常见价格示例
        long[] examplePrices = {1000, 1990, 4990, 9990};
        for (long price : examplePrices) {
            example.append(formatCurrency(price, currencyCode))
                  .append(" = ")
                  .append(price)
                  .append(" milliunits\n");
        }
        
        return example.toString();
    }
    
    /**
     * 验证价格是否在合理范围内
     * 
     * @param milliunits milliunits 金额
     * @param currencyCode 货币代码
     * @return 是否在合理范围内
     */
    public static boolean isReasonablePrice(Long milliunits, String currencyCode) {
        if (milliunits == null || currencyCode == null) {
            return false;
        }
        
        // 不同货币的合理价格范围（以milliunits为单位）
        Map<String, long[]> reasonableRanges = new HashMap<>();
        reasonableRanges.put("USD", new long[]{100, 100000}); // 0.1美元 - 100美元
        reasonableRanges.put("EUR", new long[]{100, 100000}); // 0.1欧元 - 100欧元
        reasonableRanges.put("CNY", new long[]{100, 500000}); // 0.1人民币 - 500人民币
        reasonableRanges.put("SGD", new long[]{100, 100000}); // 0.1新币 - 100新币
        
        // 获取货币的合理范围，如果没有特定设置，使用默认范围
        long[] range = reasonableRanges.getOrDefault(currencyCode, new long[]{1, 1000000});
        
        return milliunits >= range[0] && milliunits <= range[1];
    }
}