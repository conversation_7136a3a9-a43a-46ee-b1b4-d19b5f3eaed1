package com.lx.pl.pay.apple.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Apple Product Entity
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PayAppleProductDto {

    /**
     * apple 生成的产品ID
     */
    @Schema(description = "apple 生成的产品ID")
    private String appleProductId;

    /**
     * lumen 数量/month
     */
    @Schema(description = "订阅： 每月lumen数量， one time 一次性发放数量")
    private Integer lumen;


    private Integer initialLumen;

    /**
     * 计划等级
     */
    @Schema(description = "计划等级（例如：standard，pro）")
    private String planLevel;

    /**
     * 产品类型
     */
    @Schema(description = "产品类型（例如：plan，one）")
    private String productType;

    /**
     * 价格间隔
     */
    @Schema(description = "价格间隔（例如：year，month）")
    private String priceInterval;

    private String mark;

}
