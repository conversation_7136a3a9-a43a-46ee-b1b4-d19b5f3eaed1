package com.lx.pl.pay.paypal.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.paypal.api.payments.Amount;
import com.paypal.api.payments.Currency;
import com.paypal.api.payments.Links;
import com.paypal.base.rest.APIContext;
import com.paypal.base.rest.HttpMethod;
import com.paypal.base.rest.PayPalRESTException;
import com.paypal.base.rest.RESTUtil;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaypalPaymentSaleModel extends CustomPaypalResource {
    private String billingAgreementId;
    private Amount amount;
    private String paymentMode;
    private String updateTime;
    private String createTime;
    private String custom;
    private String protectionEligibilityType;
    private Currency transactionFee;
    private String reasonCode;
    private String protectionEligibility;
    private String parentPayment;
    private List<Links> links;
    private String id;
    private String state;
    private String invoiceNumber;

    /**
     * {
     * "amount": {
     * "total": "2.34",
     * "currency": "USD"
     * },
     * "using": "INVOICE_ID",
     * "payer_info": {
     * "email": "<EMAIL>"
     * }
     * }
     *
     * @param apiContext
     */
    public PaypalPaymentSaleModel refund(APIContext apiContext) throws PayPalRESTException {
//        PayPalSubPaymentRecord.gePaymentId
        String pattern = "v1/payments/sale/{sale_id}/refund";

        Object[] parameters = new Object[]{this.getId()};
        String resourcePath = RESTUtil.formatURIPath(pattern, parameters);
        PaypalPaymentSaleModel rtn = executeWithRetry(apiContext, HttpMethod.POST, resourcePath, this.toJSON(), PaypalPaymentSaleModel.class);
        return rtn;
    }

}