package com.lx.pl.pay.paypal.service.strategy;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.enums.AlarmEnum;
import com.lx.pl.pay.paypal.annotation.PaypalEvent;
import com.lx.pl.pay.paypal.model.PaypalSubscriptionModel;
import com.lx.pl.pay.paypal.model.event.PaypalSubscriptionEvent;
import com.lx.pl.pay.paypal.service.IPaypalEventHandler;
import com.lx.pl.service.message.DingTalkAlert;
import com.lx.pl.util.LogicUtil;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * BILLING.SUBSCRIPTION.EXPIRED	        订阅已到期（用户未续订）
 * BILLING.SUBSCRIPTION.CANCELLED	    订阅被取消（用户主动取消或管理员取消）
 * BILLING.SUBSCRIPTION.SUSPENDED	    订阅暂停（可能是因为支付失败）
 * BILLING.SUBSCRIPTION.RE-ACTIVATED    订阅重新激活
 */
@Component
@PaypalEvent(eventType = {"BILLING.SUBSCRIPTION.EXPIRED", "BILLING.SUBSCRIPTION.CANCELLED", "BILLING.SUBSCRIPTION.SUSPENDED", "BILLING.SUBSCRIPTION.RE-ACTIVATED"})
public class PaypalSubscriptionTerminalHandler extends IPaypalEventHandler<PaypalSubscriptionEvent> {
    @Resource
    private DingTalkAlert dingTalkAlert;

    @Override
    public void handleEvent(PaypalSubscriptionEvent data) {
        PaypalSubscriptionModel subscription = data.getModel();
//        String customId = subscription.getCustomId();
//        JSONObject entries = JSONUtil.parseObj(customId);
//        RLock lock = redissonClient.getLock(PAYPAL_USER_LOCK_PREFIX + (customId == null ? subscription.getId() : entries.getStr("userId")));
        RLock lock = redissonClient.getLock(PAYPAL_ACTION_LOCK_PREFIX + subscription.getId());
        String loginName = null;
        try {
            lock.lock();
            loginName = applicationContext.getBean(PaypalSubscriptionTerminalHandler.class).doHandleEvent(data);
            if (StrUtil.isNotBlank(loginName)) {
                vipService.resettingPersonalLumens(loginName);
            }
        } finally {
            unlockAndRefreshVip(lock, loginName);
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public String doHandleEvent(PaypalSubscriptionEvent data) {
        JSONObject entries = JSONUtil.parseObj(data.getModel().getCustomId());
        String userId = entries.getStr("userId");
        User userById = userService.getUserById(Long.parseLong(userId));
        if (Objects.equals(data.getEventType(), "BILLING.SUBSCRIPTION.SUSPENDED") || Objects.equals(data.getEventType(), "BILLING.SUBSCRIPTION.RE-ACTIVATED")) {
            //发送告警信息
            dingTalkAlert.sendAlarmMessage(LogicUtil.buildAlarmMessage(
                    AlarmEnum.AlarmTypeEnum.BUSINESS.getDescription(),
                    AlarmEnum.AlarmSourceEnum.PAY_PAL.getDescription(),
                    "PayPal subscription terminal alarm, id: " + data.getId() + ", event: " + data.getEventType(),
                    userId,
                    null));
        }
        switch (data.getEventType()) {
            case "BILLING.SUBSCRIPTION.EXPIRED":
                // 订阅已到期（用户未续订）
                log.info("subscriptionId: {} 订阅已到期（用户未续订）", data.getModel().getId());
                subscriptionCurrentService.updateAutoRenewStatus(data.getModel().getId(), 0);
                return userById.getLoginName();
            case "BILLING.SUBSCRIPTION.CANCELLED":
                // 订阅被取消（用户主动取消或管理员取消）
                log.info("subscriptionId: {} 订阅被取消（用户主动取消或管理员取消）", data.getModel().getId());
                subscriptionCurrentService.updateAutoRenewStatus(data.getModel().getId(), 0);
                return userById.getLoginName();
            case "BILLING.SUBSCRIPTION.SUSPENDED":
                // 订阅暂停（可能是因为支付失败）
                PaypalSubscriptionModel model = data.getModel();
                payPalLogicSubscriptionService.suspendedSubscription(model);

                return userById.getLoginName();
            case "BILLING.SUBSCRIPTION.RE-ACTIVATED":
                // 订阅重新激活
                payPalLogicSubscriptionService.reActivedSubscription(data.getModel());
                return userById.getLoginName();
            default:
                break;
        }
        return userById.getLoginName();
    }
}
