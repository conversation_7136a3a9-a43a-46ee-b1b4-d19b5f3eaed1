package com.lx.pl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class WatchADTaskDetailVO {

    /**
     * 已观看广告次数
     */
    @Schema(name = "已观看广告次数")
    private Integer watchedCount;

    /**
     * 最大观看次数
     */
    @Schema(name = "最大观看次数")
    private Integer totalCount;

    /**
     * 下次观看广告时间，单位秒
     */
    @Schema(name = "下次观看广告时间，单位秒")
    private String nextWatchTime;

    /**
     * 上次拉起广告时间，单位秒
     */
    @Schema(name = "上次拉起广告时间，单位秒")
    private String lastBeginWatchTime;

    /**
     * 下次完成观看广告任务可获得的奖励
     */
    @Schema(name = "下次完成观看广告任务可获得的奖励")
    private Integer rewardLumen;
}
