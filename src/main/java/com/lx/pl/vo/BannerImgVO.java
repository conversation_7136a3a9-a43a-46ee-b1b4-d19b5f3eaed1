package com.lx.pl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class BannerImgVO {

    /**
     * 图片id
     */
    @Schema(description = "图片id")
    private Long id;

    /**
     * 图片链接地址
     */
    @Schema(description = "图片链接地址")
    private String imgUrl;

    /**
     * 图片顺序
     */
    @Schema(description = "图片顺序")
    private Integer sort;

    /**
     * 图片状态：0-未发布，1-已发布
     */
    @Schema(description = "图片状态：0-未发布，1-已发布")
    private Integer status;

    /**
     * 图片跳转链接地址
     */
    @Schema(name = "图片跳转链接地址")
    private String jumpUrl;
}
