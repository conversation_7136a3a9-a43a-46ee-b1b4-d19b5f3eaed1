package com.lx.pl.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
@Schema(description = "推送消息上报请求")
public class PushMessageReportReq {

    @NotBlank(message = "messageId cannot be null")
    @Schema(description = "推送消息ID")
    private String messageId;

    @NotNull(message = "messageType cannot be null")
    @Pattern(regexp = "^push_open$", message = "invalid messageType")
    @Schema(description = "消息类型", example = "push_open")
    private String messageType;

}
